<div class="consultation-service">
    <style>
        .consultation-service .left-column > section {
            margin-bottom: 0;
        }
        .consultation-service .left-column h2 {
            margin-bottom: 0;
        }
    </style>
    
    <div class="product-header-grid">
        
        <div class="image-column">
            <div class="product-images">
                
                <div class="main-image">
                    <?php if(!empty($product->images)): ?>
                        <img src="<?php echo e($product->images[0]); ?>" alt="<?php echo e($product->name); ?>" class="featured-image">
                    <?php endif; ?>
                </div>
            </div>
        </div>

        
        <div class="info-column">
            
            <div class="status-badges <?php echo e($is_consultation ? 'invisible lg:visible lg:opacity-0' : ''); ?>">
                <?php if($product->get_standard()): ?>
                    <div class="badge active">
                        <img src="/wp-content/uploads/2024/03/certificate_icon.svg" alt="Standard" width="16"
                            height="16" />
                        <span><?php echo e(__('Standard')); ?>: <?php echo e($product->get_standard()); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            
            <h1 class="product-title"><?php echo e($product->name); ?></h1>
            <div class="product-description">
                <?php echo $product->short_description; ?>

            </div>

            
            <div class="project-meta">
                
                <div class="info">
                    <div class="info-item">
                        <strong class="w-32"><?php echo e(__('Company', CO2MARKET_TEXT_DOMAIN)); ?></strong>
                        <span class="greentext"><?php echo e($product->get_company_name()); ?></span>
                    </div>
                    <div class="info-item lower-item">
                        <strong class="w-32">Service category</strong>
                        <span class="greentext">
                            <?php
                                $categories = strip_tags(wc_get_product_category_list($product->get_id()));
                                $categories = explode(',', $categories);
                                $filtered_categories = array_filter($categories, function ($cat) {
                                    return trim($cat) !== 'Consulting service';
                                });
                                echo implode(', ', $filtered_categories);
                            ?>
                        </span>
                    </div>
                    <?php if($product->can_ask_for_offer()): ?>
                        <a href="#" class="ask-for-offer-btn popmake-1077 mt-6 flex items-center justify-center px-4 py-2 rounded-md text-sm font-normal w-fit border-2 border-ox-green-400 text-ox-green-600 bg-transparent hover:bg-ox-green-200 transition-all">
                            Ask for offer
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <?php
        $has_price = !empty($product->get_price()) && $product->get_price() !== '';
        $purchase_type = $product->get_purchase_type() ?: 'Ask for offer';
    ?>
    <div class="price-bar <?php echo e(!$has_price ? 'price-bar--no-price' : ''); ?>">
        <div class="left-price-bar">
            <div class="amount-box">
                <?php if($is_consultation): ?>
                    <?php if($product->get_time_estimate()): ?>
                    <p class="consultation-time-estimate">
                        <span class="consultation-time-estimate-time">Time</span>
                        <span class="consultation-time-estimate-estimate">estimate:</span>
                    </p>
                    <?php endif; ?>
                <?php else: ?>
                    <p class="available-amount"><?php echo e(__('Available tonnes')); ?>:</p>
                <?php endif; ?>
                <div class="underline-value">
                    <?php if($is_consultation): ?>
                        <?php if($product->get_time_estimate()): ?>
                        <p class="stock-quantity"><?php echo e($product->get_time_estimate()); ?></p>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php
                            $stock_quantity = $product->get_stock_quantity();
                            $low_stock_amount = get_option('woocommerce_notify_low_stock_amount');

                            if ($stock_quantity <= 0) {
                                echo '<p class="out-of-stock">' . __('Out of Stock') . '</p>';
                            } elseif ($stock_quantity <= $low_stock_amount) {
                                echo '<p class="low-stock">' . number_format($stock_quantity) . ' t CO₂e</p>';
                            } else {
                                echo '<p class="stock-quantity">' . number_format($stock_quantity) . ' t CO₂e</p>';
                            }
                        ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="tonne-price-box">
                <p class="tonne-price"><?php echo e(__('Type')); ?>:</p>
                <div class="underline-value">
                    <p class="pricebar-value-p">
                        <?php if($purchase_type === 'Buy now'): ?>
                            <?php echo e(__('Purchase now')); ?>

                        <?php elseif($purchase_type === 'Ask for offer'): ?>
                            <?php echo e(__('Ask for offer')); ?>

                        <?php else: ?>
                            <?php echo e(__('Inactive')); ?>

                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>

        <?php if($purchase_type === 'Buy now' && $has_price): ?>
            <form class="cart" action="<?php echo e($product->get_permalink()); ?>" method="post" enctype="multipart/form-data">
                <div class="flex items-center justify-end gap-4 p-1">
                    <div class="pricebar-value-p"><?php echo $product->get_price_html(); ?></div>
                    <button type="submit" name="add-to-cart" value="<?php echo e($product->get_id()); ?>"
                        class="single_add_to_cart_button">
                        <img src="/wp-content/uploads/2024/02/cart_icon.svg" alt="" width="18"
                            height="18" />
                        <p><?php echo e(__('Add to cart')); ?></p>
                    </button>
                </div>
            </form>
        <?php endif; ?>
    </div>

    
    <div class="product-content-grid">
        
        <div class="left-column">
            
            <section class="project-type">
                <h2>Service information</h2>
            </section>

            
            <section class="project-details">
                <div class="detail-row">
                    <div class="icon-column">
                        <?php echo str_replace(
                            '<svg',
                            '<svg data-filename="main_get_footprint.svg"',
                            file_get_contents(resource_path('svg/main_get_footprint.svg')),
                        ); ?>

                    </div>
                    <div class="content-column">
                        <h2 class="small-header">Service category</h2>
                        <p>
                            <?php
                                $categories = strip_tags(wc_get_product_category_list($product->get_id()));
                                $categories = explode(',', $categories);
                                $filtered_categories = array_filter($categories, function ($cat) {
                                    return trim($cat) !== 'Consulting service';
                                });
                                echo implode(', ', $filtered_categories);
                            ?>
                        </p>
                    </div>
                </div>
                <?php if($product->get_service_id()): ?>
                    <div class="detail-row">
                        <div class="icon-column">
                            <?php echo file_get_contents(resource_path('svg/general_id.svg')); ?>

                        </div>
                        <div class="content-column">
                            <h2 class="small-header">Service ID</h2>
                            <p><?php echo e($product->get_service_id()); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if($product->get_consultant_id()): ?>
                    <div class="detail-row">
                        <div class="icon-column">
                            <?php echo file_get_contents(resource_path('svg/general_id.svg')); ?>

                        </div>
                        <div class="content-column">
                            <h2 class="small-header">Consultant ID</h2>
                            <p><?php echo e($product->get_consultant_id()); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if($product->get_company_name()): ?>
                    <div class="detail-row">
                        <div class="icon-column">
                            <?php echo file_get_contents(resource_path('svg/general_organization.svg')); ?>

                        </div>
                        <div class="content-column">
                            <h2 class="small-header"><?php echo e(__('Company', CO2MARKET_TEXT_DOMAIN)); ?></h2>
                            <p><?php echo e($product->get_company_name()); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if($product->get_standard()): ?>
                    <div class="detail-row">
                        <div class="icon-column">
                            <?php echo str_replace(
                                '<svg',
                                '<svg data-filename="general_certification.svg"',
                                file_get_contents(resource_path('svg/general_certification.svg')),
                            ); ?>

                        </div>
                        <div class="content-column">
                            <h2 class="small-header">
                                <?php echo e(str_contains($product->get_standard(), ',') ? 'Standards' : 'Standard'); ?></h2>
                            <p><?php echo e($product->get_standard()); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </section>
        </div>

        
        <div class="right-column">
            
            <section class="about-project">
                <h3>About this service</h3>
                <div class="content">
                    <?php echo $product->get_description(); ?>

                </div>
            </section>
        </div>
    </div>
</div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/partials/consultation-product.blade.php ENDPATH**/ ?>