   <div class="compensation-project">
       

       
       <div class="product-header-grid">
           
           <div class="image-column">
               <div class="product-images">
                   
                   <div class="main-image">
                       <?php if(!empty($product->images)): ?>
                           <img src="<?php echo e($product->images[0]); ?>" alt="<?php echo e($product->name); ?>" class="featured-image">
                       <?php endif; ?>
                   </div>
               </div>
           </div>

           
           <div class="info-column">
               
               <div class="status-badges <?php echo e($is_consultation ? 'invisible lg:visible lg:opacity-0' : ''); ?>">
                   <?php
                       $is_active = get_field('active');
                   ?>
                   <?php if($is_active): ?>
                       <div class="badge active">
                           <?php echo e(svg('alert_checkmark', 'w-4 h-4')); ?>
                           <span class="text-ox-green-600 font-normal"><?php echo e(__('Active')); ?></span>
                       </div>
                   <?php else: ?>
                       <div class="badge not-active">
                           <img src="/wp-content/uploads/2024/04/not_active_icon.svg" alt="" width="16"
                               height="16" />
                           <span class="text-yellow-600 font-normal"><?php echo e(__('Inactive')); ?></span>
                       </div>
                   <?php endif; ?>

                   <?php if($product->get_certificate()): ?>
                       <div class="badge certified">
                           <img src="/wp-content/uploads/2024/03/certificate_icon.svg" alt="" width="16"
                               height="16" />
                           <span class="font-normal"><?php echo e(__('Certified by')); ?> <?php echo e($product->get_certificate()); ?></span>
                       </div>
                   <?php endif; ?>
               </div>

               
               <div class="product-description">
                   
                   <h1 class="product-title"><?php echo e($product->name); ?></h1>

                   <?php echo $product->get_short_description(); ?>

               </div>
           </div>
       </div>

       
       <div class="product-secondary-grid">
           
           <div class="secondary-left-column">
               
               <?php if(count($product->images) > 1): ?>
                   <div class="product-thumbnails-carousel">
                       <div class="swiper thumbnailSwiper">
                           <div class="swiper-wrapper">
                               <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                   <div class="swiper-slide">
                                       <div class="thumbnail-slide <?php echo e($index === 0 ? 'active' : ''); ?>">
                                           <img src="<?php echo e($image); ?>"
                                               alt="<?php echo e($product->name); ?> - Image <?php echo e($index + 1); ?>"
                                               data-index="<?php echo e($index); ?>" class="thumbnail-image">
                                       </div>
                                   </div>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </div>
                           <div class="swiper-button-next"></div>
                           <div class="swiper-button-prev"></div>
                       </div>
                   </div>
               <?php endif; ?>
           </div>
           
           <div class="secondary-right-column">
               
               <div class="project-meta">
                   <div class="score-container">
                       <div class="score-circle" style="--score: <?php echo e($product->get_score() ?? 0); ?>%">
                           <span class="score"><?php echo e($product->get_score() ? $product->get_score() : '-'); ?></span>
                       </div>
                       <?php if($product->can_ask_for_offer()): ?>
                           <a href="#" class="ask-for-offer-btn popmake-1077 mt-4 text-ox-green-600">
                               Ask for offer
                           </a>
                       <?php endif; ?>
                   </div>
                   <div class="info">
                       <div class="info-item">
                           <strong><?php echo e(__('Organization')); ?></strong>
                           <p class="greentext"><?php echo e($product->get_project_provider()); ?></p>
                       </div>
                       <div class="info-item lower-item">
                           <strong><?php echo e(__('Project type')); ?></strong>
                           <p class="greentext">
                               <?php $__currentLoopData = $product->get_project_types(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                   <?php echo e($type['name']); ?> <?php echo e($type['description']); ?><br>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </p>
                       </div>
                       <div class="info-icons">
                           <ul class="sdgs-container">
                               <?php
                                   $sdgs = $product->get_sdgs();
                                   $identified_sdgs = [];
                                   $other_sdgs = false;

                                   foreach ($sdgs as $sdg) {
                                       if (!empty($sdg['icon']) && strtolower($sdg['name']) !== 'other') {
                                           $identified_sdgs[] = $sdg;
                                       } else {
                                           $other_sdgs = true;
                                       }
                                   }
                               ?>

                               <?php $__currentLoopData = $identified_sdgs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sdg): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                   <li class="sdg-image" title="SDG <?php echo e($sdg['id']); ?>: <?php echo e($sdg['name']); ?>">
                                       <img src="<?php echo e($sdg['icon']); ?>"
                                           alt="SDG <?php echo e($sdg['id']); ?>: <?php echo e($sdg['name']); ?>" width="32"
                                           height="32" loading="lazy" />
                                   </li>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                               <?php if($other_sdgs): ?>
                                   <li class="sdg-image" title="Other SDGs">
                                       <img src="<?php echo e(@Vite::svg('sdg/sdg_placeholder_icon.svg')); ?>" alt="Other SDGs"
                                           width="32" height="32" loading="lazy" />
                                   </li>
                               <?php endif; ?>
                           </ul>
                       </div>
                   </div>
               </div>
           </div>
       </div>

       
       <?php
           $has_price = $product->get_price() && $product->get_price() !== '';
       ?>
       <div class="price-bar <?php echo e(!$has_price ? 'price-bar--no-price' : ''); ?>">
           <div class="left-price-bar">
               <?php if($has_price): ?>
                   <div class="tonne-price-box">
                       <p class="tonne-price"><?php echo e(__('Price per metric tonne')); ?>:</p>
                       <div class="underline-value">
                           <p class="pricebar-value-p"><?php echo $product->get_price_html(); ?></p>
                       </div>
                   </div>
               <?php endif; ?>
               <?php if($product->managing_stock()): ?>
                   <div class="amount-box">
                       <?php if($is_consultation): ?>
                           <p class="consultation-time-estimate">
                               <span class="consultation-time-estimate-time">Time</span>
                               <span class="consultation-time-estimate-estimate">estimate:</span>
                           </p>
                       <?php else: ?>
                           <p class="available-amount"><?php echo e(__('Available tonnes')); ?>:</p>
                       <?php endif; ?>
                       <div class="underline-value">
                           <?php if($is_consultation): ?>
                               <p class="stock-quantity"><?php echo e($product->get_time_estimate()); ?></p>
                           <?php else: ?>
                               <?php
                                   $stock_quantity = $product->get_stock_quantity();
                                   $low_stock_amount = get_option('woocommerce_notify_low_stock_amount');

                                   if ($stock_quantity <= 0) {
                                       echo '<p class="out-of-stock">' . __('Out of Stock') . '</p>';
                                   } elseif ($stock_quantity <= $low_stock_amount) {
                                       echo '<p class="low-stock">' . number_format($stock_quantity) . ' t CO₂e</p>';
                                   } else {
                                       echo '<p class="stock-quantity">' .
                                           number_format($stock_quantity) .
                                           ' t CO₂e</p>';
                                   }
                               ?>
                           <?php endif; ?>
                       </div>
                   </div>
               <?php endif; ?>
           </div>

           <?php if($has_price): ?>
               <form
                   class="cart"
                   action="<?php echo e($product->get_permalink()); ?>"
                   method="post"
                   enctype="multipart/form-data"
                   x-data="{ quantity: 50 }" 
                   @process-selected.window="quantity = $event.detail.quantity" 
               >
                   <div id="price_calculator" class="wc-measurement-price-calculator-price-table">
                       <div class="price-table-grid">
                           
                           <div class="price-table-content">
                               <div class="quantity-input-container">
                                   <input type="number" name="quantity" id="length_needed" class="amount_needed"
                                       min="1" step="1" x-model="quantity"> 
                                   <div class="units">
                                       
                                       <span class="invisible" id="hiddenValue" x-text="quantity"></span>
                                       <span class="units-value">t CO₂e</span>
                                   </div>
                               </div>
                           </div>

                           
                           <button type="submit" name="add-to-cart" value="<?php echo e($product->get_id()); ?>"
                               class="single_add_to_cart_button">
                               <img src="/wp-content/uploads/2024/02/cart_icon.svg" alt="" width="18"
                                   height="18" />
                               <p><?php echo e(__('Add to cart')); ?></p>
                           </button>

                           
                           <div id="purchase-type-container" class="disclaimer">
                               <div class="purchase-type">
                                   <p id="purchase-type-text">This is a <?php echo e($product->get_purchase_type()); ?>. Read more
                                   </p>
                                   <div class="question-circle"><span>i</span></div>
                                   <span class="disc-tooltip">
                                       Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                                   </span>
                               </div>
                           </div>

                           
                           <div class="subscription-checkbox">
                               <label for="subscription_checkbox">
                                   <input type="checkbox" name="subscription_checkbox" id="subscription_checkbox" />
                                   <p><?php echo e(__('Yearly Subscription')); ?></p>
                               </label>
                           </div>
                       </div>
                   </div>
               </form>
           <?php endif; ?>
       </div>

       
       <div class="product-content-grid">
           
           <div class="left-column">
               
               <section class="project-type">
                   <h2>Project type</h2>
                   <?php if($product->get_project_types()): ?>
                       <div class="type-card">
                           <?php $__currentLoopData = $product->get_project_types(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                               <div class="type-flex">
                                   <img src="<?php echo e($type['icon']); ?>" alt="<?php echo e($type['name']); ?>" width="33"
                                       height="33" class="rounded-md" />
                                   <div class="type-content">
                                       <h3><?php echo e($type['name']); ?></h3>
                                       <p><?php echo e($type['level2']); ?></p>
                                   </div>
                               </div>
                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                       </div>
                   <?php endif; ?>
               </section>

               
               <section class="co-benefits">
                   <h2>Co-benefits</h2>
                   <?php $__currentLoopData = $product->get_co_benefits(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                       <div class="benefit-card">
                           <?php echo file_get_contents(resource_path("svg/benefits/{$key}.svg")); ?>

                           <div class="benefit-content">
                               <h3><?php echo e($benefit['label']); ?></h3>
                               <p><?php echo e($benefit['description']); ?></p>
                           </div>
                       </div>
                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                   
                   <div class="sdg-list mt-6">
                       <?php
                           $sdgs = $product->get_sdgs();
                           $identified_sdgs = [];
                           $other_sdgs = false;

                           foreach ($sdgs as $sdg) {
                               if (!empty($sdg['icon']) && strtolower($sdg['name']) !== 'other') {
                                   $identified_sdgs[] = $sdg;
                               } else {
                                   $other_sdgs = true;
                               }
                           }
                       ?>

                       <?php $__currentLoopData = $identified_sdgs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sdg): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                           <div class="sdg-flex">
                               <img src="<?php echo e($sdg['icon']); ?>" alt="SDG <?php echo e($sdg['id']); ?>: <?php echo e($sdg['name']); ?>"
                                   width="33" height="33" />
                               <div class="sdg-content">
                                   <h3><?php echo e($sdg['name']); ?></h3>
                               </div>
                           </div>
                       <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                       <?php if($other_sdgs): ?>
                           <div class="sdg-flex">
                               <img src="<?php echo e(@Vite::svg('sdg/sdg_placeholder_icon.svg')); ?>" alt="Other SDGs"
                                   width="33" height="33" />
                               <div class="sdg-content">
                                   <h3>Other</h3>
                               </div>
                           </div>
                       <?php endif; ?>
                   </div>
               </section>

               
               <section class="project-details">
                   <?php if($product->get_project_id()): ?>
                       <div class="detail-row">
                           <div class="icon-column">
                               <?php echo str_replace(
                                   '<svg',
                                   '<svg data-filename="general_id.svg"',
                                   file_get_contents(resource_path('svg/general_id.svg')),
                               ); ?>

                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Project code</h2>
                               <p><?php echo e($product->get_project_id()); ?></p>
                           </div>
                       </div>
                   <?php endif; ?>
                   <?php if($product->get_organization()): ?>
                       <div class="detail-row">
                           <div class="icon-column">
                               <?php echo file_get_contents(resource_path('svg/general_organization.svg')); ?>

                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Organization</h2>
                               <p><?php echo e($product->get_organization()); ?></p>
                           </div>
                       </div>
                   <?php endif; ?>
                   <?php if($product->get_certificate()): ?>
                       <div class="detail-row">
                           <div class="icon-column">
                               <?php echo str_replace(
                                   '<svg',
                                   '<svg data-filename="general_certification.svg"',
                                   file_get_contents(resource_path('svg/general_certification.svg')),
                               ); ?>

                           </div>
                           <div class="content-column">
                               <h2 class="small-header">Certification</h2>
                               <p><?php echo e($product->get_certificate()); ?></p>
                           </div>
                       </div>
                   <?php endif; ?>
                   <?php if($product->get_country()): ?>
                       <div class="detail-row">
                           <div class="icon-column">
                               <?php echo str_replace(
                                   '<svg',
                                   '<svg data-filename="general_location.svg"',
                                   file_get_contents(resource_path('svg/general_location.svg')),
                               ); ?>

                           </div>
                           <div class="content-column">
                               <h2 class="small-header"><?php echo e(__('Location', CO2MARKET_TEXT_DOMAIN)); ?></h2>
                               <p><?php echo e($product->get_country()); ?></p>
                           </div>
                       </div>
                   <?php endif; ?>
               </section>
           </div>

           
           <div class="right-column">
               
               <section class="about-project">
                   <h3>About this project</h3>
                   <div class="content">
                       <?php echo $product->get_description(); ?>

                   </div>
               </section>

               
               <?php
                   $impact_description = $product->get_impact_description();
               ?>
               <?php if($impact_description && trim($impact_description) !== ''): ?>
                   <section class="impact-description">
                       <h3>Impact Description</h3>
                       <div class="content">
                           <?php echo $impact_description; ?>

                       </div>
                   </section>
               <?php endif; ?>

               
               <?php if($product->get_env_description()): ?>
                   <section class="environmental-benefits">
                       <h2>Environmental benefits</h2>
                       <div class="content">
                           <?php echo $product->get_env_description(); ?>

                       </div>
                       <?php
                           $benefits_list = $product->get_environmental_benefits_list();
                       ?>
                       <?php if(!empty($benefits_list)): ?>
                           <ul class="benefits-list">
                               <?php $__currentLoopData = $benefits_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                   <li><?php echo $benefit['benefit']; ?></li>
                               <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </ul>
                       <?php endif; ?>
                   </section>
               <?php endif; ?>

               
               <?php if($product->get_social_description()): ?>
                   <section class="social-benefits">
                       <h2>Social benefits</h2>
                       <div class="content">
                           <?php echo $product->get_social_description(); ?>

                       </div>
                   </section>
               <?php endif; ?>

               
               <?php if($product->get_country_map_url()): ?>
                   <section class="map-section">
                       <div class="gap-2">
                           <a class="text-lg text-ox-green-800 hover:text-ox-green-600 transition-colors"
                               href="https://www.google.com/maps/embed/v1/place?key=AIzaSyBf-E9K7EWlXZzJhFIuOA9I_YDg1WGXhXpD7bFII&q=<?php echo e($product->get_country_map_url()); ?>"
                               target="_blank">
                               <?php echo e(__('View on map', CO2MARKET_TEXT_DOMAIN)); ?>

                           </a>
                       </div>
                   </section>
               <?php endif; ?>
           </div>
       </div>
   </div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/partials/project-product.blade.php ENDPATH**/ ?>