<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['process', 'isSelected' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['process', 'isSelected' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    /** @var $process \App\Models\OffsetProcess */
    $step = $process->step;
    $type = $process->type;
?>

<a href="#" 
   @click.prevent="setSelectedProcess('<?php echo e($process->id); ?>'); open = false"
   class="block px-4 py-3 rounded-md"
   :class="{ 'bg-ox-green-200': selectedProcessId == '<?php echo e($process->id); ?>' }">
    <div class="flex justify-between items-center w-full">
        <div class="emission-item flex flex-col">
            <?php if (isset($component)) { $__componentOriginald9aad9a1650ef6635e125e8519f59c87 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald9aad9a1650ef6635e125e8519f59c87 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-selector.emissions','data' => ['process' => $process]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-selector.emissions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['process' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($process)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald9aad9a1650ef6635e125e8519f59c87)): ?>
<?php $attributes = $__attributesOriginald9aad9a1650ef6635e125e8519f59c87; ?>
<?php unset($__attributesOriginald9aad9a1650ef6635e125e8519f59c87); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald9aad9a1650ef6635e125e8519f59c87)): ?>
<?php $component = $__componentOriginald9aad9a1650ef6635e125e8519f59c87; ?>
<?php unset($__componentOriginald9aad9a1650ef6635e125e8519f59c87); ?>
<?php endif; ?>
            <span class="text-ox-black"><?php echo e($process?->name); ?></span>
        </div>

        <div class="flex gap-6 items-center">
            <?php if (isset($component)) { $__componentOriginal757e99ec5a1351129baf2f7bea1f8191 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal757e99ec5a1351129baf2f7bea1f8191 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-selector.step-icon','data' => ['completed' => $step->hasCompletedOffsetStep(),'name' => ''.e(__('offset')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-selector.step-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['completed' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($step->hasCompletedOffsetStep()),'name' => ''.e(__('offset')).'']); ?>
                <?php echo e(svg('main_offset')); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal757e99ec5a1351129baf2f7bea1f8191)): ?>
<?php $attributes = $__attributesOriginal757e99ec5a1351129baf2f7bea1f8191; ?>
<?php unset($__attributesOriginal757e99ec5a1351129baf2f7bea1f8191); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal757e99ec5a1351129baf2f7bea1f8191)): ?>
<?php $component = $__componentOriginal757e99ec5a1351129baf2f7bea1f8191; ?>
<?php unset($__componentOriginal757e99ec5a1351129baf2f7bea1f8191); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal757e99ec5a1351129baf2f7bea1f8191 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal757e99ec5a1351129baf2f7bea1f8191 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.process-selector.step-icon','data' => ['completed' => $step->hasCompletedCommunicateStep(),'name' => ''.e(__('communicate')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('process-selector.step-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['completed' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($step->hasCompletedCommunicateStep()),'name' => ''.e(__('communicate')).'']); ?>
                <?php echo e(svg('main_communicate')); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal757e99ec5a1351129baf2f7bea1f8191)): ?>
<?php $attributes = $__attributesOriginal757e99ec5a1351129baf2f7bea1f8191; ?>
<?php unset($__attributesOriginal757e99ec5a1351129baf2f7bea1f8191); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal757e99ec5a1351129baf2f7bea1f8191)): ?>
<?php $component = $__componentOriginal757e99ec5a1351129baf2f7bea1f8191; ?>
<?php unset($__componentOriginal757e99ec5a1351129baf2f7bea1f8191); ?>
<?php endif; ?>

            <?php if($type): ?>
                <?php if (isset($component)) { $__componentOriginal2ddbc40e602c342e508ac696e52f8719 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2ddbc40e602c342e508ac696e52f8719 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.badge','data' => ['color' => $type->toColor()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($type->toColor())]); ?>
                    <?php echo e(Str::title($type->toLabel())); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2ddbc40e602c342e508ac696e52f8719)): ?>
<?php $attributes = $__attributesOriginal2ddbc40e602c342e508ac696e52f8719; ?>
<?php unset($__attributesOriginal2ddbc40e602c342e508ac696e52f8719); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2ddbc40e602c342e508ac696e52f8719)): ?>
<?php $component = $__componentOriginal2ddbc40e602c342e508ac696e52f8719; ?>
<?php unset($__componentOriginal2ddbc40e602c342e508ac696e52f8719); ?>
<?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</a>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/process-selector/calculation-item.blade.php ENDPATH**/ ?>