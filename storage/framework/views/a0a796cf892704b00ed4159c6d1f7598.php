<?php
    global $post;
    $post_type = get_post_type($post);
?>

<div class="breadcrumbs-container">
    <a class="back-button" href="/">
        <img src="/wp-content/uploads/2024/05/arrow_icon.svg" alt="arr" width="15">
        <span>Back to search</span>
    </a>
    
    <div class="custom-breadcrumbs">
        <a href="/">Home</a>
        
        <?php if(is_single()): ?>
            <?php if($post_type === 'footprint-example'): ?>
                <a href="/calculate">Get footprint</a>
                <a href="/calculate/explore-examples">Business examples</a>
                <a style="opacity: 100%" href="<?php echo e(get_permalink()); ?>"><?php echo e(get_the_title()); ?></a>
                
            <?php elseif($post_type === 'company'): ?>
                <a href="/calculate">Get footprint</a>
                <a href="/calculate/search-consultants">Consultants</a>
                <a style="opacity: 100%" href="<?php echo e(get_permalink()); ?>"><?php echo e(get_the_title()); ?></a>
                
            <?php elseif($post_type === 'product'): ?>
                <?php
                    $id = $post->ID;
                    $is_compensation = has_term('compensation-project', 'product_cat', $id) || 
                                     has_term('korvaushanke', 'product_cat', $id);
                ?>
                
                <?php if($is_compensation): ?>
                    <?php
                        $company = get_field('project_provider', $id);
                    ?>
                    <a href="/offset">Offset</a>
                    <a href="/offset-projects">Offset projects</a>
                    <?php if($company): ?>
                        <a href="#"><?php echo e($company); ?></a>
                    <?php endif; ?>
                <?php else: ?>
                    <?php
                        $company = get_field('company_name', $id);
                        $url_slug = str_replace(" ", "-", strtolower($company));
                    ?>
                    <a href="/calculate">Get footprint</a>
                    <a href="/calculate/search-consultants">Consultants</a>
                    <?php if($company): ?>
                        <a href="/post/company/<?php echo e($url_slug); ?>"><?php echo e($company); ?></a>
                    <?php endif; ?>
                <?php endif; ?>
                <a style="opacity: 100%" href="<?php echo e(get_permalink()); ?>"><?php echo e(get_the_title()); ?></a>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div><?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/breadcrumbs-bar.blade.php ENDPATH**/ ?>