<?php
    global $post;
    $is_current_post_in_category = in_category($category->term_id, $post);
    
    // Get posts for this category
    $posts = get_posts([
        'category' => $category->term_id,
        'orderby' => 'title',
        'order' => 'ASC',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ]);
?>

<div class="secondary-category">
    <input 
        type="checkbox" 
        id="category-<?php echo e($category->slug); ?>" 
        class="accordion-toggle" 
        <?php echo e($is_current_post_in_category ? 'checked' : ''); ?>

    >
    <label for="category-<?php echo e($category->slug); ?>" class="accordion-label">
        <?php echo e(esc_html($category->name)); ?>

    </label>
    
    <div class="accordion-content">
        <?php if($posts): ?>
            <ul class="posts-list">
                <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post_item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $is_current_post = ($post_item->ID == $post->ID);
                        $highlight_class = $is_current_post ? 'current-post-item' : '';
                    ?>
                    <li class="post-item <?php echo e($highlight_class); ?>">
                        <a href="<?php echo e(get_permalink($post_item)); ?>">
                            <?php echo e(get_the_title($post_item)); ?>

                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        <?php else: ?>
            <p><?php echo e($isGeneral ? 'No posts found in General category.' : 'No posts found.'); ?></p>
        <?php endif; ?>
    </div>
</div> <?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/partials/category-accordion.blade.php ENDPATH**/ ?>