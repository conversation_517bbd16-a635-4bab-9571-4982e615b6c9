<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'completed' => false,
    'name' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'completed' => false,
    'name' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="{ showTooltip: false }" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" class="relative">
    <?php echo e($slot); ?>

    <?php if($completed): ?>
        <?php echo e(svg('alert_checkmark', ['class' => 'absolute size-[1.8em]', 'style' => 'top: -1em; right: -1.2em;'])); ?>
    <?php endif; ?>
</div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/process-selector/step-icon.blade.php ENDPATH**/ ?>