<?php echo app('Illuminate\Foundation\Vite')('resources/css/product.css'); ?>

<?php $__env->startSection('content'); ?>
    <?php
        $product = new App\Models\Product($post->ID);
        $is_compensation = $product->is_compensation_project();
        $is_consultation = $product->is_consultation();
    ?>
    <div class="product-page ">
        <?php echo do_shortcode('[breadcrumbs_bar]'); ?>

        
       <div class="mb-8">
     
        <?php if(session()->has('wc_cart_success')): ?>
            <?php
                $message = session()->get("wc_cart_success");
            ?>
            <?php if (isset($component)) { $__componentOriginale7e83721b35db7e5b45147171079da7e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale7e83721b35db7e5b45147171079da7e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.cart-message','data' => ['type' => 'success','message' => ''.e($message).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('cart-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'success','message' => ''.e($message).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale7e83721b35db7e5b45147171079da7e)): ?>
<?php $attributes = $__attributesOriginale7e83721b35db7e5b45147171079da7e; ?>
<?php unset($__attributesOriginale7e83721b35db7e5b45147171079da7e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale7e83721b35db7e5b45147171079da7e)): ?>
<?php $component = $__componentOriginale7e83721b35db7e5b45147171079da7e; ?>
<?php unset($__componentOriginale7e83721b35db7e5b45147171079da7e); ?>
<?php endif; ?>
        <?php endif; ?>
        
        <?php if(session()->has('wc_cart_error')): ?>
            <?php
                $message = session()->get("wc_cart_error");
            ?>
            <?php if (isset($component)) { $__componentOriginale7e83721b35db7e5b45147171079da7e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale7e83721b35db7e5b45147171079da7e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.cart-message','data' => ['type' => 'error','message' => ''.e($message).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('cart-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'error','message' => ''.e($message).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale7e83721b35db7e5b45147171079da7e)): ?>
<?php $attributes = $__attributesOriginale7e83721b35db7e5b45147171079da7e; ?>
<?php unset($__attributesOriginale7e83721b35db7e5b45147171079da7e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale7e83721b35db7e5b45147171079da7e)): ?>
<?php $component = $__componentOriginale7e83721b35db7e5b45147171079da7e; ?>
<?php unset($__componentOriginale7e83721b35db7e5b45147171079da7e); ?>
<?php endif; ?>
        <?php endif; ?>
        </div>
        
        <?php if($is_compensation): ?>
            <?php echo $__env->make('partials.project-product', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php elseif($is_consultation): ?>
            <?php echo $__env->make('partials.consultation-product', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the main image element
            const mainImage = document.querySelector('.main-image img');

            // Initialize Swiper with proper navigation configuration
            const thumbnailSwiper = new Swiper('.thumbnailSwiper', {
                slidesPerView: 4,
                spaceBetween: 16,
                watchSlidesProgress: true,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                // Enable loop mode for continuous sliding
                loop: true,
                // Add slideToClickedSlide for better UX
                slideToClickedSlide: true,
                breakpoints: {
                    320: {
                        slidesPerView: 2,
                        spaceBetween: 8
                    },
                    480: {
                        slidesPerView: 3,
                        spaceBetween: 12
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 16
                    }
                }
            });

            // Update thumbnail active states and main image
            thumbnailSwiper.on('slideChange', function() {
                const activeSlide = thumbnailSwiper.slides[thumbnailSwiper.activeIndex];
                const thumbnailImage = activeSlide.querySelector('.thumbnail-image');

                if (thumbnailImage && mainImage) {
                    mainImage.src = thumbnailImage.src;
                }

                // Update active states
                document.querySelectorAll('.thumbnail-slide').forEach(slide => {
                    slide.classList.remove('active');
                });
                activeSlide.querySelector('.thumbnail-slide').classList.add('active');
            });

            // Handle thumbnail click
            document.querySelectorAll('.swiper-slide').forEach(slide => {
                slide.addEventListener('click', function() {
                    const thumbnailImage = this.querySelector('.thumbnail-image');
                    if (thumbnailImage && mainImage) {
                        mainImage.src = thumbnailImage.src;
                    }

                    // Update active state
                    document.querySelectorAll('.thumbnail-slide').forEach(slide => {
                        slide.classList.remove('active');
                    });
                    this.querySelector('.thumbnail-slide').classList.add('active');
                });
            });

            // Carbon footprint calculation script
            const hiddenValue = document.getElementById('hiddenValue');
            const input = document.getElementById('length_needed');

            if (input && hiddenValue) {
                wp.data.subscribe(function() {
                    const process = wp.data.select('compensationStore').getSelectedProcess();
                    const carbonFootprint = process && process.calculation && process.calculation
                        .carbonFootprint ? process.calculation.carbonFootprint : 50;

                    // Round the carbon footprint to the nearest whole number (tonne)
                    const roundedFootprint = (carbonFootprint < 1) ? 1 : Math.round(carbonFootprint);
                    input.value = roundedFootprint;
                    hiddenValue.innerHTML = roundedFootprint;
                });

                input.addEventListener('input', () => {
                    hiddenValue.innerHTML = input.value;
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/woocommerce/single-product.blade.php ENDPATH**/ ?>