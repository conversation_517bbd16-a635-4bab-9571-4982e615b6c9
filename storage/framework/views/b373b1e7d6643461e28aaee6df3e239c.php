<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['color' => 'blue']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['color' => 'blue']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $baseClasses = 'inline-flex items-center rounded-md px-2.5 py-1 text-xs font-medium';
    $colorClasses = [
        'blue' => 'bg-ox-blue-400 text-ox-blue-600',
        'green' => 'bg-ox-green-400 text-ox-green-600',
        'red' => 'bg-ox-warning-secondary text-ox-black',
        'orange' => 'bg-ox-orange-400 text-ox-orange-600',
        'gray' => 'bg-gray-100 text-gray-800',
    ];
?>

<span <?php echo e($attributes->merge(['class' => $baseClasses . ' ' . ($colorClasses[(string) $color] ?? $colorClasses['gray'])])); ?>>
    <?php echo e($slot); ?>

</span><?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/components/badge.blade.php ENDPATH**/ ?>