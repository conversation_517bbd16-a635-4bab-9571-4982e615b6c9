
<div <?php echo e($attributes); ?> class="w-full">
  <style>
    .breadcrumbs-block button:not(:last-child) {
      color: #c8dcc8;
      max-width: 24ch;
      text-wrap: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .business-example-search-container button:focus,
    .business-example-search-container button:hover,
    .breadcrumbs-block button:focus,
    .breadcrumbs-block button:hover {
      background-color: transparent;
      color: #0a2222;
    }

   .breadcrumbs-block button:not(:first-of-type)::before {
      content: "•";
      padding: 0 1rem;
      opacity: 50%;
    }

    .prev-btn-image {
      filter: brightness(0) saturate(100%);
    }

    .dynamic-header {
      color: #0a2222 !important;
    }

    .card-title-text {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; /* Limit to 3 lines */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }

    @media (max-width: 768px) {
      .business-example-grid {
        max-height: 500px;
      }
    }

    .search-container form {
      width: 80%;
    }

    .search-container .is-search-form label .is-search-input {
      color: #c8dcc8 !important;
      border-radius: 5px !important;
      padding: 0.8em 1.5em;
      font-size: 1rem !important;
      background-color: #fff !important;
      height: 2.5em;
      font-weight: 400;
      border: 2px solid #e1f0dc !important;
      display: flex !important;
    }
    .search-container .is-search-form label .is-search-input:focus {
      border: 2px solid #a5e6aa !important;
      color: #286444 !important;
      outline: none;
    }

    #is-ajax-search-result-6188 .is-highlight {
      background-color: #a5e6aa !important;
    }

    .business-example-card-image {
      aspect-ratio: 4/3;
      object-fit: cover;
    }
  </style>

  <div
    x-data="businessExampleSearch()"
    class="w-full business-example-search-container"
  >
    <div
      class="search-container justify-around"
      :style="{ display: level === 0 ? 'flex' : 'none' }"
    >
      [ivory-search id="18742" title="business example search tab"]
    </div>

    <div class="mt-[50px] p-1">
      <!-- Breadcrumbs -->
      <template x-if="0 < level">
        <div class="breadcrumbs-block flex flex-row items-center space-x-1 text-[#0A2222] text-[0.875rem] mb-[4rem]">
          <div class="text-[#286444] bg-[#A5E6AA] px-[1rem] w-[45px] h-[45px] content-center rounded-[5px] text-[1.5rem] font-bold mr-[1rem] flex items-center justify-center">
            <span x-text="level + 1"></span>
          </div>
          <button
            class="text-[#0A2222] cursor-pointer"
            @click="handleBacktrackClick(0)"
          >
            All industries
          </button>
          <template x-if="selected.level1">
            <button
              class="text-[#0A2222] cursor-pointer"
              @click="handleBacktrackClick(1)"
              x-text="selected.level1"
            ></button>
          </template>
          <template x-if="selected.level2">
            <button
              class="text-[#0A2222] cursor-pointer"
              @click="handleBacktrackClick(2)"
              x-text="selected.level2"
            ></button>
          </template>
          <template x-if="selected.level3">
            <button
              class="text-[#0A2222] cursor-pointer"
              @click="handleBacktrackClick(3)"
              x-text="selected.level3"
            ></button>
          </template>
          <template x-if="selected.level4">
            <button
              class="text-[#0A2222] cursor-pointer"
              @click="handleBacktrackClick(4)"
              x-text="selected.level4"
            ></button>
          </template>
          <template x-if="selected.level5">
            <button
              class="text-[#0A2222] cursor-pointer"
              @click="handleBacktrackClick(5)"
              x-text="selected.level5"
            ></button>
          </template>
        </div>
      </template>

      <h3 class="text-left dynamic-header text-[1.5rem] text-[#0A2222] font-bold tracking-wider leading-[1.4rem]">
        BUSINESS CATEGORIES
      </h3>

      <!-- Grid Container -->
      <div class="business-example-grid mt-4">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-5">
          <!-- Loading State -->
          <template x-if="loading">
            <template x-for="(_, i) in Array(10)" :key="i">
              <div class="flex flex-col">
                <div class="w-full bg-[#C8DCC8] h-[145px] rounded-[20px]"></div>
                <div class="w-full mt-[0.5rem] bg-[#C8DCC8] h-[1rem] rounded-[5px]"></div>
              </div>
            </template>
          </template>

          <!-- Data State -->
          <template x-if="!loading">
            <template x-for="(data, i) in categories" :key="i">
              <div class="flex flex-col h-full">
                <!-- Level 5 cards with URL -->
                <template x-if="level === 5">
                  <a :href="data.url" class="flex flex-1 h-full justify-center w-full cursor-pointer">
                    <div class="w-full">
                      <img
                        class="rounded-[20px] business-example-card-image w-full"
                        :src="data.image"
                        :alt="data.title"
                      />
                      <p
                        class="card-title-text text-[0.875rem] leading-none tracking-normal text-center text-[#0A2222] px-5 mt-[1rem]"
                        :title="data.title"
                        x-text="data.title"
                      ></p>
                    </div>
                  </a>
                </template>
                <!-- Cards for levels 0-4 -->
                <template x-if="level !== 5">
                  <button
                    class="flex flex-1 h-full justify-center w-full cursor-pointer"
                    @click="handleCardClick(level, data.title)"
                  >
                    <!-- Card Component -->
                    <div class="w-full">
                      <img
                        class="rounded-[20px] business-example-card-image w-full"
                        :src="data.image"
                        :alt="data.title"
                      />
                      <p
                        class="card-title-text text-[0.875rem] leading-none tracking-normal text-center text-[#0A2222] px-5 mt-[1rem]"
                        :title="data.title"
                        x-text="data.title"
                      ></p>
                    </div>
                  </button>
                </template>
              </div>
            </template>
          </template>
        </div>
      </div>

      <!-- Previous Button -->
      <template x-if="level !== 0">
        <button
          class="text-[#0A2222] text-[0.875rem] mt-[2rem] flex cursor-pointer w-fit items-baseline"
          @click="handleBacktrackClick(level - 1)"
        >
          <img
            class="prev-btn-image"
            src="/wp-content/uploads/2024/05/arrow_icon.svg"
            alt="backtrack"
            width="15"
          />
          <p class="ml-[0.5rem]">Previous</p>
        </button>
      </template>
    </div>
  </div>

  <script>
    document.addEventListener('alpine:init', () => {
      Alpine.data('businessExampleSearch', () => ({
        categories: [],
        loading: true,
        level: 0,
        value: '',
        selected: {
          level1: '',
          level2: '',
          level3: '',
          level4: '',
          level5: ''
        },

        init() {
          this.fetchData();
        },

        async fetchData() {
          this.loading = true;
          try {
            const response = await fetch(`/api/v1/business-examples?level=${this.level}&value=${this.value}`);
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            const data = await response.json();
            this.categories = data;
          } catch (error) {
            console.error('Error fetching data:', error);
            this.categories = [];
          } finally {
            this.loading = false;
          }
        },

        handleCardClick(level, value) {
          this.selected[`level${level + 1}`] = value;

          if (level < 5) {
            this.level = level + 1;
            this.value = value;
            this.fetchData();
          }
          // Level 5 cards are handled directly via URL links
        },

        handleBacktrackClick(previous) {
          if (previous < 0) return;

          if (previous < 1) {
            this.value = '';
            this.selected = {
              level1: '',
              level2: '',
              level3: '',
              level4: '',
              level5: ''
            };
          } else if (previous < 2) {
            this.value = this.selected.level1;
            this.selected.level2 = '';
            this.selected.level3 = '';
            this.selected.level4 = '';
            this.selected.level5 = '';
          } else if (previous < 3) {
            this.value = this.selected.level2;
            this.selected.level3 = '';
            this.selected.level4 = '';
            this.selected.level5 = '';
          } else if (previous < 4) {
            this.value = this.selected.level3;
            this.selected.level4 = '';
            this.selected.level5 = '';
          } else if (previous < 5) {
            this.value = this.selected.level4;
            this.selected.level5 = '';
          }

          this.level = previous;
          this.fetchData();
        }
      }));
    });
  </script>
</div>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/blocks/business-example-search.blade.php ENDPATH**/ ?>