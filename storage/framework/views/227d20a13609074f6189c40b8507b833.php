<?php
    // Fetch and sort categories
    $categories = get_the_category();
    $priority_slugs = ['get-footprint', 'offset', 'communicate'];

    // Sort categories to prioritize specific slugs
    usort($categories, function ($a, $b) use ($priority_slugs) {
        $a_priority = in_array($a->slug, $priority_slugs) ? 0 : 1;
        $b_priority = in_array($b->slug, $priority_slugs) ? 0 : 1;
        return $a_priority - $b_priority ?: $a->term_id <=> $b->term_id;
    });

    // Get sharing URLs
    $url_to_share = get_permalink();
    $title_to_share = get_the_title();
?>

<article <?php echo post_class('h-entry'); ?>>
  <div class="article-background"></div>
  <div class="article-container">
    <header>
      <div class="article-header-grid">
        
        <div class="article-image-column">
          <?php if(has_post_thumbnail()): ?>
            <div class="post-thumb-img-content">
              <?php echo get_the_post_thumbnail(); ?>

            </div>
          <?php endif; ?>
        </div>

        
        <div class="article-content-column">
          
          <div class="entry-meta">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <?php if(!in_array($category->slug, ['uncategorized', 'another-slug-to-exclude'])): ?>
                <a href="<?php echo e(esc_url(get_category_link($category->term_id))); ?>" class="ast-terms-link">
                  <?php echo e(esc_html($category->name)); ?>

                </a>
              <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>

          
          <h2 class="p-name">
            <?php echo $title; ?>

          </h2>

          
          <div class="entry-meta-icons-grid">
            
            <div class="entry-meta-icon-column">
              <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode($url_to_share)); ?>" target="_blank">
                <?php echo file_get_contents(resource_path('svg/some/facebook_icon.svg')); ?>

              </a>
            </div>

            
            <div class="entry-meta-icon-column">
              <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode($url_to_share)); ?>&text=<?php echo e(urlencode($title_to_share)); ?>" target="_blank">
                <?php echo file_get_contents(resource_path('svg/some/x_icon.svg')); ?>

              </a>
            </div>

            
            <div class="entry-meta-icon-column" onclick="copyToClipboard()" style="cursor:pointer;">
              Share
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="article-content-layout">
      
      <div class="article-sidebar">
        
        <div class="sidebar-search">
          <?php echo do_shortcode('[ivory-search id="19265" title="Article search form"]'); ?>

        </div>

        
        <div class="posts-category-container">
          <?php
            $all_categories = get_categories(['hide_empty' => false]);

            // Display primary categories
            foreach ($priority_slugs as $slug) {
              $primary_category = get_category_by_slug($slug);
              if ($primary_category) {
                echo "<h2>" . esc_html($primary_category->name) . "</h2>";

                // Display secondary categories
                foreach ($all_categories as $category) {
                  if ($category->parent === $primary_category->term_id) {
                    echo view('partials.category-accordion', [
                      'category' => $category,
                      'isGeneral' => false
                    ])->render();
                  }
                }
              }
            }

            // Display General section
            echo "<h2>General</h2>";
            foreach ($all_categories as $category) {
              if (!in_array($category->slug, $priority_slugs) && $category->parent == 0) {
                echo view('partials.category-accordion', [
                  'category' => $category,
                  'isGeneral' => true
                ])->render();
              }
            }
          ?>
        </div>
      </div>

      
      <div class="article-main-content">
        <div class="e-content post-content-wrapper">
          <?php echo the_content(); ?>

        </div>
      </div>
    </div>

    <?php if($pagination): ?>
      <footer>
        <nav class="page-nav" aria-label="Page">
          <?php echo $pagination; ?>

        </nav>
      </footer>
    <?php endif; ?>

    <?php if(!is_singular('post')): ?>
      <?php (comments_template()); ?>
    <?php endif; ?>
  </div>
</article>

<script>
function copyToClipboard() {
  navigator.clipboard.writeText('<?php echo e(get_permalink()); ?>').catch(function(err) {
    console.error('Could not copy text: ', err);
  });
}
</script>
<?php /**PATH /Users/<USER>/Herd/co2market/wp-content/themes/co2market/resources/views/partials/content-single.blade.php ENDPATH**/ ?>