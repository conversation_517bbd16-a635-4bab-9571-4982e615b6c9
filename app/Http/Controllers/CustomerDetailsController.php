<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

class CustomerDetailsController extends Controller
{
    /**
     * Handle the customer details form submission
     *
     * @param Request $request
     * @return mixed
     */
    public function submit(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'first_name' => 'required|string|max:64',
            'last_name' => 'required|string|max:64',
            'accept_terms' => 'required|accepted',
        ]);

        // Get the current user ID
        $user_id = get_current_user_id();

        if ($user_id) {
            // Rate-limit submissions using Laravel's RateLimiter (15-second cooldown)
            $rateLimitKey = 'customer-details-submit:' . $user_id;

            if (RateLimiter::tooManyAttempts($rateLimitKey, 1)) {
                $seconds = RateLimiter::availableIn($rateLimitKey);
                return back()->withErrors([
                    'rate_limit' => __('You can only submit the form once every 15 seconds. Please wait ' . $seconds . ' more seconds and try again.', CO2MARKET_TEXT_DOMAIN)
                ]);
            }

            // Hit the rate limiter (1 attempt per 15 seconds)
            RateLimiter::hit($rateLimitKey, 15);

            // Check if the form has already been submitted
            $additional_details_completed = get_user_meta($user_id, 'additional_details_completed', true);
            if ($additional_details_completed === 'true') {
                return back()->withErrors([
                    'already_submitted' => __('You have already submitted this form.', CO2MARKET_TEXT_DOMAIN)
                ]);
            }

            // Additional validation for field lengths
            if (strlen($validated['first_name']) > 64) {
                return back()->withErrors([
                    'first_name' => __('First name cannot exceed 64 characters.', CO2MARKET_TEXT_DOMAIN)
                ])->withInput();
            }

            if (strlen($validated['last_name']) > 64) {
                return back()->withErrors([
                    'last_name' => __('Last name cannot exceed 64 characters.', CO2MARKET_TEXT_DOMAIN)
                ])->withInput();
            }

            // Sanitize all input fields
            $first_name = sanitize_text_field($validated['first_name']);
            $last_name = sanitize_text_field($validated['last_name']);

            // Store user metadata
            update_user_meta($user_id, 'first_name', $first_name);
            update_user_meta($user_id, 'last_name', $last_name);
            
            // Mark that additional details are completed
            update_user_meta($user_id, 'additional_details_completed', 'true');
            
            // Update terms acceptance metadata
            update_user_meta($user_id, 'accepted_terms', 'true');
            update_user_meta($user_id, 'accepted_terms_time', current_time('mysql'));

            // Redirect to the homepage or dashboard with a success message
            return wp_redirect(home_url('/'));
        }

        // If no user is logged in, redirect back with an error
        return wp_redirect(wp_get_referer() ?: home_url('/'));
    }
} 