<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CurrencyService;
use Illuminate\Support\Facades\Log;

class UpdateProductPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:update-prices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all product prices based on native currency exchange rates';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting product price update...');

        try {
            $currencyService = new CurrencyService();
            $stats = $currencyService->updateAllProductPrices();

            // Output statistics
            $this->info('Price update completed successfully!');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Products', $stats['total']],
                    ['Updated Products', $stats['updated']],
                    ['EUR Products Updated', $stats['eur_updated']],
                    ['Currency Changed', $stats['currency_changed']],
                    ['Skipped Products', $stats['skipped']],
                    ['Errors', $stats['errors']],
                ]
            );

            // If there are protected rates due to fluctuations, show them
            if (!empty($stats['protected_rates'])) {
                $this->warn('Some currencies had significant exchange rate fluctuations:');
                
                $rateData = [];
                foreach ($stats['protected_rates'] as $currency => $data) {
                    $rateData[] = [
                        $currency,
                        $data['previous_rate'],
                        $data['current_rate'],
                        $data['fluctuation_percent'] . '%'
                    ];
                }
                
                $this->table(
                    ['Currency', 'Previous Rate', 'Current Rate', 'Fluctuation'],
                    $rateData
                );

                // Log significant exchange rate fluctuations to logtail for monitoring
                Log::channel('logtail')->warning('Significant exchange rate fluctuations detected', [
                    'command' => 'products:update-prices',
                    'protected_rates' => $stats['protected_rates'],
                    'timestamp' => current_time('mysql'),
                    'context' => [
                        'type' => 'exchange_rate_fluctuation',
                        'requires_review' => true,
                        'impact' => 'Some product prices were not updated due to significant rate changes'
                    ]
                ]);
            }

            // Log successful update to logtail for tracking
            Log::channel('logtail')->info('Product price update completed successfully', [
                'command' => 'products:update-prices',
                'stats' => $stats,
                'timestamp' => current_time('mysql'),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'execution_context' => [
                    'type' => 'scheduled_command_success',
                    'total_products' => $stats['total'],
                    'updated_products' => $stats['updated'],
                    'errors' => $stats['errors'],
                    'has_protected_rates' => !empty($stats['protected_rates'])
                ]
            ]);

            // Log the update to WordPress options
            update_option('co2market_last_scheduled_update', current_time('mysql'));
            update_option('co2market_last_scheduled_update_stats', $stats);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Error updating product prices: ' . $e->getMessage());

            // Critical error - needs immediate attention via error_stack (Slack + Logtail)
            Log::channel('error_stack')->critical('Product price update failed', [
                'command' => 'products:update-prices',
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'timestamp' => current_time('mysql'),
                'context' => [
                    'type' => 'scheduled_command_failure',
                    'severity' => 'critical',
                    'requires_immediate_attention' => true,
                    'impact' => 'Product prices may be outdated, affecting customer pricing'
                ]
            ]);

            // Also log to logtail for detailed tracking
            Log::channel('logtail')->error('Product price update command failed', [
                'command' => 'products:update-prices',
                'exception' => [
                    'class' => get_class($e),
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                    'previous' => $e->getPrevious() ? [
                        'class' => get_class($e->getPrevious()),
                        'message' => $e->getPrevious()->getMessage(),
                    ] : null,
                ],
                'timestamp' => current_time('mysql'),
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
            ]);

            // Log the error to WordPress options for admin dashboard visibility
            update_option('co2market_last_scheduled_update_error', [
                'message' => $e->getMessage(),
                'time' => current_time('mysql'),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'logged_to_slack' => true
            ]);

            return Command::FAILURE;
        }
    }
} 