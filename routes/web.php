<?php

use Illuminate\Support\Facades\Route;
use App\Http\Middleware\WordPress;
use App\Http\Middleware\HandleInertiaRequests;
use App\Services\ChatService;
use App\Http\Controllers\VendorDetailsController;
use App\Http\Controllers\CustomerDetailsController;
use Inertia\Inertia;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application.
|
*/

Route::get('/login', function () {
    return view('template-login');
})->middleware(['web', WordPress::class])->name('login');

Route::get('/register', function () {
    return view('template-register');
})->middleware(['web', WordPress::class])->name('register');

Route::get('/lost-password', function () {
    return view('template-lost-password');
})->middleware(['web', WordPress::class])->name('lost-password');

Route::get('/cart', function () {
    return view('cart');
})->middleware(['web', WordPress::class])->name('cart');


Route::get('/offset-projects', function () {
    return view('pages/offset-projects');
})->middleware(['web', WordPress::class])->name('offset-projects');

Route::get('/services', function () {
    return view('pages/services');
})->middleware(['web', WordPress::class])->name('services');

Route::get('/additional-vendor-details', function () {
    return view('pages/additional-vendor-details');
})->middleware(['web', WordPress::class])->name('additional-vendor-details');

/**
 * Customer Details Form Routes
 */
Route::get('/additional-customer-details', function () {
    return view('pages/additional-customer-details');
})->middleware(['web', WordPress::class])->name('additional-customer-details');

Route::post('/customer-details-submit', [CustomerDetailsController::class, 'submit'])
    ->middleware(['web', WordPress::class])
    ->name('customer-details-submit');

/**
 * Vendor Details Form Routes
 */
Route::post('/vendor-details-submit', [VendorDetailsController::class, 'submit'])
    ->middleware(['web', WordPress::class])
    ->name('vendor-details-submit');

// Handle all my-account routes with a catch-all pattern
Route::get('/my-account/{path?}', function () {
    $chat_services = new ChatService();
    $token = $chat_services->get_user_auth_token();
    if ($token) {
        $auth_token = $token["authToken"];
        $user_id = $token["userId"];
        setcookie('chat_token', $auth_token, time() + 86400, COOKIEPATH, COOKIE_DOMAIN, is_ssl(), false);
        setcookie('chat_user_id', $user_id, time() + 86400, COOKIEPATH, COOKIE_DOMAIN, is_ssl(), false);
    }

    // Get current WordPress user information using our User model
    $user_data = null;

    $user = \App\Models\User::getCurrentUser();
    $user_data = $user->toArray();

    return Inertia::render('my-account/index', [
        'user' => $user_data,
    ]);
})->where('path', '.*')->middleware(['web', WordPress::class, HandleInertiaRequests::class]);
