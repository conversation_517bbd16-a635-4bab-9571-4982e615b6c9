<?php

use App\Http\Controllers\Api\TestController;
use App\Http\Controllers\Api\ImportFootprintController;
use App\Http\Controllers\Api\BusinessExampleController;
use App\Http\Controllers\Api\FootprintCalculateController;
use App\Http\Controllers\Api\TranslationController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ProductController;
use App\Services\ChatService;
use App\Http\Middleware\HandleInertiaRequests;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Middleware\WordPress;
use App\Http\Middleware\ApiErrorLog;
use \Illuminate\Support\Facades\Log;
use App\Models\Post;
use App\Services\CurrencyService;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::prefix('api/v1')->middleware([ApiErrorLog::class])->group(function () {
    Route::get('/test', [TestController::class, 'index'])->name('test');
    Route::get("/test-db", function () {
        $posts = Post::limit(10)->get();
        return response()->json($posts);
    });
    Route::post('/import-footprint/upload', [ImportFootprintController::class, 'upload'])
        ->middleware(['web', WordPress::class])
        ->name('import-footprint.upload');

    // Order API - Get all orders for current user
    Route::get('/orders', [OrderController::class, 'getUserOrders'])
        ->middleware(['web', WordPress::class])
        ->name('api.orders.user');

    // Order API - Get single order by ID
    Route::get('/orders/{id}', [OrderController::class, 'getOrder'])
        ->middleware(['web', WordPress::class])
        ->name('api.orders.show');

    // Product API
    Route::get('/products', [ProductController::class, 'getUserProducts'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.user');

    Route::get('/products/allowed-types', [ProductController::class, 'getAllowedProductTypes'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.allowed-types');

    Route::get('/products/form-options', [ProductController::class, 'getFormOptions'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.form-options');

    Route::post('/products', [ProductController::class, 'store'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.store');

    Route::get('/products/{id}', [ProductController::class, 'show'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.show');

    Route::put('/products/{id}', [ProductController::class, 'update'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.update');

    Route::delete('/products/{id}', [ProductController::class, 'archive'])
        ->middleware(['web', WordPress::class])
        ->name('api.products.archive');

    Route::get('/log', function () {
        Log::channel('error_stack')->critical("Test notification");
        return "log sent";
    });

    // Business Examples Search API
    Route::get('/business-examples', [BusinessExampleController::class, 'index'])
        ->middleware(['web', WordPress::class])
        ->name('business-examples');

    // Footprint Calculate API
    Route::post('/footprint-calculate', [FootprintCalculateController::class, 'store'])
        ->middleware(['web', WordPress::class])
        ->name('footprint-calculate.store');

    // Translation API
    Route::post('/translate', [TranslationController::class, 'translate'])
        ->middleware(['web', WordPress::class, 'throttle:60,1'])
        ->name('translate');

    // Organization API
    require __DIR__ . '/api/organization.php';
});
