@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
    :root {
      --background: 0 0% 100%;
      --foreground: 240 10% 3.9%;
  
      --card: 0 0% 100%;
      --card-foreground: 240 10% 3.9%;
   
      --popover: 0 0% 100%;
      --popover-foreground: 240 10% 3.9%;
   
      --primary: 240 5.9% 10%;
      --primary-foreground: 0 0% 98%;
   
      --secondary: 240 4.8% 95.9%;
      --secondary-foreground: 240 5.9% 10%;
   
      --muted: 240 4.8% 95.9%;
      --muted-foreground: 240 3.8% 46.1%;
   
      --accent: 240 4.8% 95.9%;
      --accent-foreground: 240 5.9% 10%;
   
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;
  
      --border: 240 5.9% 90%;
      --input: 240 5.9% 90%;
      --ring: 240 10% 3.9%;
   
      --radius: 0.5rem;
    }
   
    .dark {
      --background: 240 10% 3.9%;
      --foreground: 0 0% 98%;
   
      --card: 240 10% 3.9%;
      --card-foreground: 0 0% 98%;
   
      --popover: 240 10% 3.9%;
      --popover-foreground: 0 0% 98%;
   
      --primary: 0 0% 98%;
      --primary-foreground: 240 5.9% 10%;
   
      --secondary: 240 3.7% 15.9%;
      --secondary-foreground: 0 0% 98%;
   
      --muted: 240 3.7% 15.9%;
      --muted-foreground: 240 5% 64.9%;
   
      --accent: 240 3.7% 15.9%;
      --accent-foreground: 0 0% 98%;
   
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;
   
      --border: 240 3.7% 15.9%;
      --input: 240 3.7% 15.9%;
      --ring: 240 4.9% 83.9%;
    }
  }
   



body{
    background-color: white;
    font-family: 'Rubik', sans-serif;
}

.entry-title {
    display: none;
    }

/* Typography */
p {
    color: theme('colors.ox-black');
    font-family: 'Rubik', sans-serif;
    font-weight: 400;
    line-height: 1.6;
}

.small-text {
    font-size: 0.875rem;
}

h1 {
    color: theme('colors.ox-green.600');
    font-size: 3.75rem;
    font-weight: 400;
    line-height: 1.2em;
    text-transform: uppercase;
}

h2 {
    color: theme('colors.ox-green.600');
    font-size: 3rem;
    font-weight: 400;
    line-height: 1.3em;
    text-transform: uppercase;
}

h3 {
    color: theme('colors.ox-black');
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.4em;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

input {
    border: 2px solid theme('colors.ox-green.400');
    padding: 6px 11px;
    border-radius: 5px;
}


.textField {
    background: white;
    border: 2px solid theme('colors.ox-green.200');
    box-sizing: border-box;
    border-radius: 8px;
    padding: 8px 16px;
    width: 100%;
    -webkit-appearance: none;
    appearance: none;
    -moz-appearance: none;
    background-image: url('@svg/down-arrow.svg');
    background-repeat: no-repeat;
    background-size: 14px 14px;
    background-position: calc(100% - 16px);
}

/* Buttons */
.button {
    padding: 0.8em 1em;
    font-size: 1rem;
    border-radius: 5px;
    transition: all 100ms ease;
    font-weight: 500;
}

.uagb-tab {
    border: 2px solid theme('colors.ox-green.400') !important;
    border-radius: 5px !important;
}

.uagb-tabs__active {
    background-color: theme('colors.ox-green.400') !important;
    color: theme('colors.ox-green.600') !important;
}

/* Fix focus and visited styles for UAGB tabs */
.uagb-tabs__wrap ul.uagb-tabs__panel li.uagb-tab a:focus,
.uagb-tabs__wrap ul.uagb-tabs__panel li.uagb-tab a:focus-visible,
.uagb-tabs__wrap ul.uagb-tabs__panel li.uagb-tab a:visited {
    outline: 2px solid theme('colors.ox-green.400') !important;
    outline-offset: 2px !important;
    border-color: transparent !important;
    box-shadow: none !important;
}

/* Fix focus and visited styles for header search */
.is-search-form .is-search-input:focus,
.is-search-form .is-search-input:focus-visible,
.is-search-form .is-search-input:visited {
    border: 2px solid theme('colors.ox-green.400') !important;
    color: theme('colors.ox-green.600') !important;
    outline: none !important;
    box-shadow: none !important;
}

/* Additional styles for Ivory Search in header */
#is-ajax-search-result-5909 .is-highlight,
#is-ajax-search-result-18742 .is-highlight,
#is-ajax-search-result-5931 .is-highlight,
.is-highlight.term-0 {
    background-color: theme('colors.ox-green.200') !important;
    color: theme('colors.ox-green.600') !important;
    font-size: inherit !important; /* Ensure highlighted text has the same font size */
    font-weight: inherit !important; /* Preserve the original font weight */
    font-family: inherit !important; /* Preserve the original font family */
}

/* Styles for Ivory Search input fields */
.is-form-id-5909 .is-search-input,
.is-form-id-5931 .is-search-input,
#is-search-input-5931,
input[id="is-search-input-5931"],
.is-search-form input.is-search-input {
    color: #c8dcc8 !important;
    border-radius: 5px !important;
    padding: 0.8em 1.5em !important;
    font-size: 1rem !important;
    background-color: #fff !important;
    height: 2.5em !important;
    font-weight: 400 !important;
    border: 2px solid #e1f0dc !important;
    display: flex !important;
    min-width: 300px !important; /* Increased default width to fit text */
    width: auto !important; /* Allow it to grow if needed */
}

.is-form-id-5909 .is-search-input:focus,
.is-form-id-5909 .is-search-input:focus-visible,
.is-form-id-5931 .is-search-input:focus,
.is-form-id-5931 .is-search-input:focus-visible,
#is-search-input-5931:focus,
#is-search-input-5931:focus-visible,
input[id="is-search-input-5931"]:focus,
input[id="is-search-input-5931"]:focus-visible {
    border-color: theme('colors.ox-green.400') !important;
    border: 2px solid theme('colors.ox-green.400') !important;
    color: theme('colors.ox-green.600') !important;
    outline: none !important;
    box-shadow: none !important;
}

.is-form-id-5909 .is-search-submit,
.is-form-id-5931 .is-search-submit,
input[type="submit"].is-search-submit {
    background-color: transparent !important;
    color: theme('colors.ox-green.600') !important;
    border: none !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    padding: 0.8em 1.5em !important;
    transition: color 0.2s ease-in-out !important;
}

/* UAGB Container Styles */
.uagb-block-358bc419 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* UAGB Button Styles */
.uagb-buttons-repeater .uagb-button__link,
a.uagb-buttons-repeater {
    background-color: theme('colors.ox-green.400') !important; /* Green-400 background */
    color: theme('colors.ox-green.600') !important;
    border: none !important;
    border-radius: 5px !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    padding: 0.325em 0.5em !important; /* Reduced horizontal padding by half */
    line-height: 1 !important; /* Keeping reduced line height */
    height: auto !important; /* Let height be determined by content */
    min-width: 0 !important; /* Allow button to be narrower */
    width: auto !important; /* Allow button to size to content */
    display: inline-flex !important; /* Better control over dimensions */
    align-items: center !important; /* Vertically center content */
    justify-content: center !important; /* Horizontally center content */
    transition: all 0.2s ease-in-out !important;
    text-decoration: none !important;
}

.is-form-id-5909 .is-search-submit:hover,
.is-form-id-5931 .is-search-submit:hover,
input[type="submit"].is-search-submit:hover {
    color: theme('colors.ox-green.600') !important;
    background-color: transparent !important;
}

/* UAGB Button Hover */
.uagb-buttons-repeater:hover,
.uagb-buttons-repeater .uagb-button__link:hover {
    color: theme('colors.ox-green.600') !important;
    background-color: theme('colors.ox-green.400') !important; /* Keep same background on hover */
    padding: 0.325em 0.5em !important; /* Reduced horizontal padding by half */
    line-height: 1 !important; /* Keeping reduced line height */
    height: auto !important;
    min-width: 0 !important;
    width: auto !important;
}

.is-form-id-5909 .is-search-submit:focus,
.is-form-id-5909 .is-search-submit:focus-visible,
.is-form-id-5931 .is-search-submit:focus,
.is-form-id-5931 .is-search-submit:focus-visible,
input[type="submit"].is-search-submit:focus,
input[type="submit"].is-search-submit:focus-visible {
    outline: 2px solid theme('colors.ox-green.400') !important;
    outline-offset: 2px !important;
    color: theme('colors.ox-green.600') !important;
    background-color: transparent !important;
}

/* UAGB Button Focus */
.uagb-buttons-repeater:focus,
.uagb-buttons-repeater:focus-visible,
.uagb-buttons-repeater .uagb-button__link:focus,
.uagb-buttons-repeater .uagb-button__link:focus-visible {
    outline: 2px solid white !important;
    outline-offset: 2px !important;
    color: theme('colors.ox-green.600') !important;
    background-color: theme('colors.ox-green.400') !important; /* Keep same background on focus */
    box-shadow: 0 0 0 2px theme('colors.ox-green.400'), 0 0 0 4px white !important; /* Double outline effect */
    padding: 0.325em 0.5em !important; /* Reduced horizontal padding by half */
    line-height: 1 !important; /* Keeping reduced line height */
    height: auto !important;
    min-width: 0 !important;
    width: auto !important;
}

.wp-block-uagb-container.uagb-block-350bc419 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}


/* Ensure consistent font sizing in search results */
.is-ajax-search-result .is-ajax-search-post .is-search-content,
.is-ajax-search-result .is-ajax-search-post .is-search-content *,
.is-ajax-search-result .is-ajax-search-post .is-search-content .is-highlight,
.is-ajax-search-result .is-ajax-search-post .is-search-content .is-highlight * {
    font-size: 14px !important;
    line-height: 1.4 !important;
}

/* Green Button */
.button-green.button-filled {
    background-color: theme('colors.ox-green.400');
    color: theme('colors.ox-green.600');
}

.button-green.button-filled:hover {
    background-color: theme('colors.ox-green.200');
}

.button-green.button-outline {
    border: 2px solid theme('colors.ox-green.400');
    color: theme('colors.ox-green.400');
    background-color: transparent;
}

.button-green.button-outline:hover {
    background-color: theme('colors.ox-green.100');
}

.button-green:focus {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 3px;
}

/* Orange Button */
.button-orange.button-filled {
    background-color: theme('colors.ox-orange.400');
    color: theme('colors.white');
}

.button-orange.button-filled:hover {
    background-color: theme('colors.ox-orange.300');
}

.button-orange.button-outline {
    border: 2px solid theme('colors.ox-orange.400');
    color: theme('colors.ox-orange.400');
    background-color: transparent;
}

.button-orange.button-outline:hover {
    background-color: theme('colors.ox-orange.300/10');
}

.button-orange:focus {
    outline: 2px solid theme('colors.ox-orange.400');
    outline-offset: 3px;
}

/* Blue Button */
.button-blue.button-filled {
    background-color: theme('colors.ox-blue.400');
    color: theme('colors.white');
}

.button-blue.button-filled:hover {
    background-color: theme('colors.ox-blue.300');
}

.button-blue.button-outline {
    border: 2px solid theme('colors.ox-blue.400');
    color: theme('colors.ox-blue.400');
    background-color: transparent;
}

.button-blue.button-outline:hover {
    background-color: theme('colors.ox-blue.200');
}

.button-blue:focus {
    outline: 2px solid theme('colors.ox-blue.400');
    outline-offset: 3px;
}

.button-secondary {
    border: 2px solid theme('colors.ox-black');
}

/* Disabled state */
.button-disabled {
    background-color: theme('colors.gray.200') !important;
    color: theme('colors.gray.500') !important;
    border-color: theme('colors.gray.200') !important;
    cursor: not-allowed;
}

/* Create Offset Project Button */
.create-offset-project-button {
    padding: 0;
    font-size: 1rem;
    border-radius: 5px;
    background-color: theme('colors.ox-green.400');
    color: theme('colors.ox-green.600');
    transition: background-color 100ms ease;
    border: none;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.create-offset-project-button:hover {
    background-color: theme('colors.ox-green.200');
}

.create-offset-project-button:focus,
.create-offset-project-button:focus-visible {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

/* Communicate CTA Button */
.communicate-cta-button {
    transition: background-color 100ms ease;
}

.communicate-cta-button:hover {
    background-color: theme('colors.ox-green.200');
}

.communicate-cta-button:focus,
.communicate-cta-button:focus-visible {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

/* Get Footprint Button */
.get-footprint-button {
    transition: background-color 100ms ease;
}

.get-footprint-button:hover {
    background-color: theme('colors.ox-green.200');
}

.get-footprint-button:focus,
.get-footprint-button:focus-visible {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

.progress-step:hover .progress-inner,
.progress-step:hover svg {
    color: theme('colors.ox-green.600');
    fill: theme('colors.ox-green.600');
}
.swiper-pagination-bullet-active {
    background-color: theme("colors.ox-green.500") !important;
  }


.badge {
    padding: 0.5em 0.75em;
    border-radius: 5px;
    font-size: 0.75rem;
    font-weight: 400;
    display: flex;
    gap: 4px;
    text-align: center;
    align-items: center;
}

.badge.green {
    background-color: theme('colors.ox-green.200');
    color: theme('colors.ox-green.600');
}

.badge.score {
    background-color: theme('colors.ox-green.400');
    color: theme('colors.ox-green.600');
    display: flex;
    align-items: center;
}

.badge.score svg {
    fill: theme('colors.ox-green.600');
}

.badge.score .flex-wrap {
    display: flex;
    flex-wrap: nowrap;
}

@media (max-width: 400px) {
    .badge.score .flex-wrap {
        flex-wrap: wrap;
    }
}

.content-button {
    @apply px-4 py-2 text-ox-black hover:text-ox-green-600 cursor-pointer relative text-base font-normal;
    margin-bottom: -1px;
    outline: none;
}

.content-button:focus {
    outline: none;
}

.content-button.active {
    @apply text-ox-green-600 font-medium;
}

.content-button.active::after {
    content: '';
    @apply absolute bottom-0 left-1/2 -translate-x-1/2 h-[2px] bg-ox-green-400;
    width: 90%;
}

/* Content Boxes */
.company-info .content-box {
    @apply mb-12 mt-16;
}

.company-info .content-box .products-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* About Us Section */
.company-info #about-us {
    @apply bg-white rounded-lg p-2;
}

.company-info #about-us h2 {
    @apply text-2xl font-bold mb-4 text-ox-black;
}

/* Breadcrumbs */
.breadcrumbs-container {
    @apply flex gap-8 items-center my-8 mb-12;
}

.breadcrumbs-container .custom-breadcrumbs {
    @apply text-sm;
    line-height: 2;
}

.breadcrumbs-container .custom-breadcrumbs a:not(:last-child) {
    @apply text-ox-green-300;
}

.breadcrumbs-container .custom-breadcrumbs a:last-child {
    @apply text-ox-green-600;
}

.breadcrumbs-container .custom-breadcrumbs a:not(:last-child)::after {
    content: '\2022';
    @apply px-4 text-ox-black;
}

.breadcrumbs-container .back-button {
    @apply flex gap-2 border-solid py-2 px-4 leading-[30px] text-base
           rounded-full border-2 transition-all duration-200
           text-[#286444] border-[#A5E6AA] m-0;
}

.breadcrumbs-container .back-button:hover {
    @apply bg-[#E1F0DC];
}

.breadcrumbs-container .back-button:focus {
    @apply outline-none;
    outline: 2px solid #A5E6AA;
    outline-offset: 2px;
}

@media (max-width: 768px) {
    .breadcrumbs-container .back-button {
        @apply text-sm w-[7.5rem];
    }

    .breadcrumbs-container .back-button span {
        @apply hidden;
    }

    .breadcrumbs-container .back-button img {
        @apply w-[15px];
    }

    .breadcrumbs-container .back-button::after {
        content: 'Back';
    }

    .company-info .two-column-layout {
        @apply ml-0;
        padding-left: 0;
        margin-left: 0 !important;
    }

    /* Move company info to bottom */
    .company-info .secondary-info-container .sidebar {
        order: 3 !important;
    }

    /* Company info mobile alignment */
    .company-info .content-column {
        @apply text-left;
    }

    .company-info .sidebar-header {
        @apply text-left;
    }

    .company-info .side-box {
        @apply justify-start;
    }

    .company-info .address-details {
        @apply text-left;
    }

    .company-info .side-box-content {
        @apply items-start;
    }

    .company-info .two-column-layout {
        @apply items-start;
    }
}

@media (max-width: 544px) {
    .breadcrumbs-container .custom-breadcrumbs a:not(:last-child):not(:first-child) {
        @apply hidden;
    }

    .breadcrumbs-container .custom-breadcrumbs a:not(:last-child)::after {
        content: '\2022  \2022  \2022';
        @apply px-4;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    /* Main Layout adjustments */
    .company-info .main-info-container,
    .company-info .secondary-info-container {
        @apply flex-col;
    }

    .company-info .main-info-container .sidebar,
    .company-info .secondary-info-container .sidebar {
        @apply w-full order-1;
    }

    .company-info .main-info-container .main-content-container,
    .company-info .secondary-info-container .main-content-container {
        @apply order-2;
    }

    /* Move company info to bottom */
    .company-info .secondary-info-container .sidebar {
        order: 3 !important;
    }

    /* Company Image repositioning */
    .company-info .sidebar .image {
        @apply -mt-[60px] ml-8;
        margin-right: auto;
        margin-left: 0.5rem !important;
    }

    /* Adjust sidebar boxes */
    .company-info .side-box,
    .company-info .sidebar-header {
        @apply ml-0;
    }

    /* Keep text left-aligned above 768px */
    .company-info .content-column {
        @apply text-left;
    }

    /* Remove left padding from article main content */
    .article-main-content {
        padding-left: 0;
    }
}

@media (max-width: 768px) {
    /* Product card layout changes */
    .project-card {
        @apply flex-col p-6;
    }

    .project-card .project-image-container {
        width: 100%;
        height: 150px;
        @apply mb-6 mx-0;
    }

    /* Card header adjustments */
    .project-card .card-header {
        @apply flex-col items-start gap-4 !important;
    }

    .project-card .type-container {
        @apply pr-0 pb-4;
    }

    .project-card .type-container::after {
        @apply hidden;
    }

    .project-card .meta-info {
        @apply pl-0;
    }

    /* Card footer adjustments */
    .project-card .card-footer {
        @apply flex-col items-start gap-4 !important;
    }

    .project-card .price-action {
        @apply flex-row items-center justify-between gap-4 w-full;
    }

    .project-card .price {
        @apply text-left;
    }

    .project-card .cart-button {
        @apply justify-center;
    }

    .project-card .ask-for-offer-btn {
        @apply justify-center w-full;
    }

    /* Content navigation */
    .company-info .button-container {
        @apply gap-4;
    }

    /* Banner height adjustment */
    .company-info .banner {
        height: 150px;
    }

    /* Hide location icon and center content column text */
    .company-info .icon-column svg {
        @apply hidden;
    }

    /* Center text on mobile */
    .company-info .content-column {
        @apply text-center;
    }

    /* Additional company info mobile adjustments */
    .company-info .two-column-layout {
        @apply ml-0;
        padding-left: 0;
        margin-left: 0 !important;
    }

    .company-info .side-box {
        @apply ml-0 flex justify-start;
        margin-left: 0 !important;
        padding-left: 0;
    }

    .company-info .sidebar-header {
        @apply ml-0 text-left;
        margin-left: 0 !important;
    }

    .company-info .small-header {
      @apply text-left;
  }

    .company-info .side-box-content {
        @apply ml-0;
        padding-left: 0;
        margin-left: 0;
    }

    .company-info .address-details {
        @apply ml-0;
        padding-left: 0;
    }

    .company-info .two-column-layout {
        @apply ml-0;
        padding-left: 0;
        margin-left: 0 !important;
    }
}

@media (max-width: 544px) {
    /* Further adjustments for smaller screens */
    .company-info {
        @apply px-4;
    }

    .project-card {
        @apply p-4;
    }

    .company-info .sidebar .image {
        @apply h-[180px] w-[180px] -mt-[40px];
    }

    /* Adjust content spacing */
    .content-box > h3:first-of-type {
        @apply p-0;
    }

    /* Navigation buttons */
    .content-button {
        @apply px-2 py-1 text-sm;
    }
}

/* Company Info Sidebar */
.company-info .sidebar-header {
    @apply text-ox-black text-lg font-bold mb-4 ml-12;
    margin-top: 0.5rem;  /* Added margin-top to create space below description */
}

.company-info .side-box {
    @apply bg-white rounded-lg mb-4 ml-12;
    padding: 1rem 0;  /* 1rem for top/bottom, 0 for left/right */
}

.company-info .side-box-content {
    @apply flex flex-col;
}

.company-info .two-column-layout {
    @apply grid grid-cols-[auto_1fr] gap-4;
    margin-left: 5px;  /* Changed to 5px indentation from COMPANY INFO */
}

.company-info .icon-column {
    @apply flex items-start;
}

.company-info .content-column {
    @apply flex flex-col gap-2 pt-[4px]; /* Added 4px top padding */
}

.company-info .side-box svg {
    @apply w-6 h-6;
}

.company-info .small-header {
    font-size: 0.875rem;
    font-weight: 700;
    text-transform: initial;
    color: theme('colors.ox-black');
}

/* Keep the address details as normal paragraph text */
.company-info .address-details p {
    @apply font-normal; /* Ensure normal weight for the actual address text */
}

/* Company Image */
.company-info .sidebar .image {
    @apply bg-white -mt-[80px] ml-12 border-solid rounded-full border-[#A5E6AA]
           h-[220px] w-[220px] flex overflow-hidden
           border-2 ring-1 ring-ox-green-400;
}

.company-info .sidebar .image img {
    @apply block self-center h-auto w-auto max-h-full max-w-full object-contain;
}

/* Secondary Info Container */
.company-info .secondary-info-container {
    @apply w-full flex gap-8;
}

/* Secondary Info Container columns */
.company-info .secondary-info-container .sidebar {
    @apply w-80 flex-shrink-0;
}

.company-info .secondary-info-container .main-content-container {
    @apply flex-grow;
}

/* Product Cards */
.project-card {
    background-color: theme('colors.ox-green.100');
    @apply overflow-hidden flex p-8 rounded-lg;
}

/* Left side - Image */
.project-card .project-image-container {
    width: 150px;
    height: 100px;
    @apply relative overflow-hidden flex-shrink-0 rounded-lg;
    background-color: theme('colors.ox-green.100');
    margin-right: 32px;
}

.project-card .project-image-container img {
    @apply w-full h-full object-cover;
}

/* Right side - Content */
.project-card .project-card-content {
    @apply flex-grow flex flex-col;
}

/* Top row - Categories and Meta */
.project-card .card-header {
    @apply flex items-center gap-4 mb-4;
}

.project-card .type-container {
    @apply flex items-center gap-2 relative pr-6;
}

/* Add vertical divider after categories */
.project-card .type-container::after {
    content: '';
    @apply absolute right-0 top-1/2 -translate-y-1/2 w-[1px] h-5 bg-ox-black;
}

.project-card .type {
    @apply bg-ox-green-200 text-ox-green-600 px-3 py-1 rounded-md text-sm font-medium;
}

.project-card .meta-info {
    @apply flex items-center gap-6 pl-6;
}

.project-card .rating,
.project-card .comments {
    @apply flex items-center gap-2;
}

.project-card .rating img,
.project-card .comments img {
    @apply w-[18px] h-[18px];
}

.project-card .rating span,
.project-card .comments span {
    @apply text-sm text-ox-black leading-none;
}

/* Middle - Title and Info */
.project-card .card-body {
    @apply flex-grow;
}

.project-card .product-title {
    @apply mb-4 mt-0;
}

.project-card .product-title a {
    @apply text-[18px] font-bold text-ox-black no-underline hover:text-ox-green-600 transition-colors leading-tight tracking-normal;
    text-transform: none;
}

/* Bottom - Price and Action */
.project-card .card-footer {
    @apply flex items-center justify-end gap-4 mt-6;
}

.project-card .price {
    @apply text-xl font-bold text-ox-green-600 bg-ox-green-200 rounded-md px-4 py-2;
}

/* Middle - Info Grid */
.project-card .info-grid {
    @apply space-y-2;
}

.project-card .info-row {
    @apply flex items-center gap-12;
}

.project-card .info-row .label {
    @apply text-sm font-bold text-ox-black min-w-[120px];
}

.project-card .info-row .value {
    @apply text-sm text-ox-black;
}

.project-card .price-action {
    @apply flex items-center gap-4 ml-auto;
}

.project-card .cart-button {
    @apply flex items-center gap-2 bg-ox-green-400 text-ox-green-600 px-4 py-2 rounded-md text-sm font-medium
           hover:bg-ox-green-200 transition-colors duration-200;
}

.project-card .ask-for-offer-btn {
    @apply flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium w-fit;
    border: 2px solid theme('colors.ox-green.400');
    color: theme('colors.ox-green.400');
    background-color: transparent;
    transition: all 100ms ease;
}

.project-card .ask-for-offer-btn:hover {
    background-color: theme('colors.ox-green.200');
}

.project-card .ask-for-offer-btn:focus {
    outline: 2px solid theme('colors.ox-green.400');
    outline-offset: 2px;
}

/* Products Grid */
.products-grid {
    @apply flex flex-col gap-6;
}

/* Category Headers */
.category-header {
    @apply text-xl font-bold text-ox-black;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
}

.category-header:first-child {
    margin-top: 0;
}

/* Content Box */
.content-box {
    @apply mb-12 mt-16;
}

.content-box .products-grid {
    @apply flex flex-col gap-6;
}

.content-box h3 {
    @apply mb-6;
}

/* Ensure first h3 in each tab has consistent spacing */
.content-box > h3:first-of-type {
    @apply mt-0 p-2;
}

/* Subsequent h3s within the same tab get top margin */
.content-box > h3:not(:first-of-type) {
    @apply mt-12;
}

.content-box + .content-box {
    @apply mt-16;
}

/* Icon Styles */
.rating svg,
.comments svg {
    @apply w-6 h-6 fill-ox-black;
}

/* Company header star icon */
.inner-rating svg {
    @apply w-10 h-10 fill-ox-black;
}

.cart-button svg {
    @apply w-7 h-7 fill-ox-green-600;
}

/* Adjust icon container spacing */
.rating,
.comments,
.cart-button {
    @apply flex items-center gap-2;
}

/* Company Info Icons */
.company-info .side-box svg {
    @apply w-6 h-6;
}

/* Location icon specific color */
.company-info .address svg {
    @apply fill-ox-black;
}

/* Contact icon specific color */
.company-info .contact svg {
    @apply fill-ox-green-600;
}

/* Popup Styles */
.pum-container {
    /* Title styling using existing h2 styles */
    .pum-title {
        color: theme('colors.ox-green.600');
        font-size: 3rem;
        font-weight: 400;
        line-height: 1.3em;
    }

    /* Form styling */
    .wpforms-container {
        /* Input fields and textareas share common styles */
        input, textarea {
            @apply text-lg rounded-md border-2 border-ox-green-200;
            color: theme('colors.ox-green.200');

            &:focus {
                @apply border-ox-green-400;
                color: theme('colors.ox-green.600');
                box-shadow: none;
                outline: none;
            }
        }

        /* Textarea specific styles */
        textarea {
            @apply p-2;

            &::placeholder {
                line-height: 27px;
                letter-spacing: normal;
            }
        }

        /* Form labels */
        .wpforms-field-label {
            @apply text-sm text-ox-black font-normal text-left mb-[5px];
        }

        /* Submit button using existing button styles */
        .wpforms-submit {
            @apply px-6 py-3 text-base font-normal rounded-md transition-colors duration-100;
            background-color: theme('colors.ox-green.400');
            color: theme('colors.ox-green.600');
            border: none;
            box-shadow: none;
            letter-spacing: normal;

            &:hover {
                background-color: theme('colors.ox-green.200');
            }

            &:focus {
                color: theme('colors.ox-green.600');
                outline: 2px solid theme('colors.ox-green.400');
                outline-offset: 2px;
                box-shadow: none;
            }
        }
    }

    /* Close button */
    .pum-content + .pum-close {
        background-color: theme('colors.ox-green.400');
        text-shadow: none;
        margin: 2rem 1em 0 0;

        &:hover {
            background-color: theme('colors.ox-green.200');
        }
    }
}

/* WooCommerce Message Styles */
/* Success Message */
.woocommerce-message {
    @apply flex flex-row items-baseline justify-between rounded-md;
    border: 2px solid theme('colors.ox-green.500');
    padding-left: 5rem;
    background-color: theme('colors.ox-green.100') !important;
    color: theme('colors.ox-black');

    &:before {
        color: theme('colors.ox-green.600') !important;
        padding: 0.5rem;
    }

    &:after {
        content: none;
    }

    &#text {
        padding-top: 3rem;
    }

    .button.wc-forward {
        @apply text-base rounded-md transition-colors duration-100;
        padding: 0.8em 1.5em !important;
        background-color: theme('colors.ox-green.400') !important;
        color: theme('colors.ox-green.600') !important;
        margin-left: 40% !important;

        &:hover {
            background-color: white !important;
        }
    }
}

/* WooCommerce Button Overrides */
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) button.button,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) .button.wc-forward,
.woocommerce button.button,
.woocommerce .button.wc-forward {
    @apply transition-colors duration-100 !important;
    display: flex !important;
    background-color: theme('colors.ox-green.400') !important;
    color: theme('colors.ox-green.600') !important;
    font-weight: 500 !important;

    &:hover {
        background-color: theme('colors.ox-green.200') !important;
        color: theme('colors.ox-green.600') !important;
    }

    &:focus {
        outline: 2px solid theme('colors.ox-green.400');
        outline-offset: 2px;
    }
}

/* Error Message */
.woocommerce-notices-wrapper {
    .woocommerce-error {
        @apply rounded-md;
        border: transparent;
        background-color: theme('colors.ox-orange.200') !important;
        color: theme('colors.ox-black');

        li {
            @apply flex items-center;
            padding-left: 5% !important;
        }

        .button.wc-forward {
            background-color: theme('colors.ox-orange.400') !important;
            color: theme('colors.ox-black') !important;

            &:hover {
                background-color: white !important;
            }
        }
    }
}

/* Article Styles */
article.h-entry {
  position: relative;
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

.article-background {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 22rem;
  left: 0;
  right: 0;
  border-radius: 10px;
  background-color: theme('colors.ox-green.100');
  z-index: 0;
}

.article-container {
  position: relative;
  z-index: 1;
  width: min(100%, 1200px);
  margin: 0 auto;
  padding: 0 2rem;
}

/* Article Header Layout */
article.h-entry header {
  position: relative;
  background: transparent;
  margin: 3rem auto 0;
  width: 100%;
}

/* Article Header Grid */
.article-header-grid {
  display: grid;
  grid-template-columns: minmax(400px, 1fr) 1fr;
  gap: 4em;
  margin: 0 auto 2em;
  position: relative;
  z-index: 1;
}

/* Left Column - Image */
.article-image-column {
  grid-column: 1;
  margin-top: 0;
  width: 100%;
  position: relative;
  z-index: 1;
}

.article-image-column .post-thumb-img-content {
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.article-image-column .post-thumb-img-content img {
  width: 100%;
  height: auto;
  object-fit: cover;
  aspect-ratio: 4/3;
  border-radius: 20px;
}

/* Right Column - Content */
.article-content-column {
  grid-column: 2;
  display: flex;
  flex-direction: column;
  gap: 1.5em;
  padding-top: 2rem;
}

/* Entry Meta (Categories) */
.article-content-column .entry-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
  align-items: center;
  padding-top: 0;
  margin-bottom: 1.5em;
}

/* Article Title */
.article-content-column .p-name {
  color: theme('colors.ox-green.600');
  font-size: 2.2rem !important;
  font-weight: 400;
  line-height: 1.2em !important;
  text-transform: uppercase;
  margin: 0 0 1.5em 0;
  padding: 0;
}

/* Social Share Icons */
.article-content-column .entry-meta-icons-grid {
  display: flex;
  gap: 5px;
  margin: 0 0 2em 0;
}

.entry-meta-icons-grid .entry-meta-icon-column {
  background-color: theme('colors.ox-green.400');
  color: theme('colors.ox-green.600');
  border-radius: 5px;
  width: auto;
  height: 2.5em; /* Match category tag height */
  padding: 0.25em; /* Reduced padding */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 200ms ease;
}

.entry-meta-icons-grid .entry-meta-icon-column:hover {
  background-color: theme('colors.ox-green.200');
}

.entry-meta-icons-grid .entry-meta-icon-column svg {
  width: 32px;
  height: 32px;
  fill: theme('colors.ox-green.600');
}

.entry-meta-icons-grid .entry-meta-icon-column:last-of-type {
  padding: 1.25rem 0.8rem; /* Adjusted vertical padding to match other buttons */
  font-size: 14px;
  font-weight: 700;
}

/* Search Block */
.my-custom-search {
  width: 100%;
  margin: 0;
}

/* Hide author and date */
.entry-meta .posted-by,
.entry-meta .posted-on,
.entry-meta br {
  display: none !important;
}

/* Category Links */
.entry-meta .ast-terms-link {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  border-radius: 5px;
  padding: 0.4em 0.8em;
  font-size: 14px;
  white-space: nowrap;
  background-color: theme('colors.ox-green.400');
  color: theme('colors.ox-green.600') !important;
  font-weight: 700;
  border: 2px solid theme('colors.ox-green.400');
  transition: all 200ms ease;
}

.entry-meta .ast-terms-link:hover {
  background-color: theme('colors.ox-green.200');
  border-color: theme('colors.ox-green.200');
}

.entry-meta .ast-terms-link:nth-child(2) {
  background-color: theme('colors.ox-green.200');
  border: 2px solid theme('colors.ox-green.200');
}

.entry-meta .ast-terms-link:nth-child(2):hover {
  border: 2px solid theme('colors.ox-green.400');
  background-color: theme('colors.ox-green.200');
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .article-header-grid {
    grid-template-columns: 1fr 1.5fr;
    gap: 4em;
  }

  .article-content-column .p-name {
    font-size: 2.4rem;
  }
}

@media (max-width: 768px) {
  .article-header-grid {
    grid-template-columns: 1fr;
    gap: 2em;
  }

  .article-image-column,
  .article-content-column {
    grid-column: 1;
  }

  .article-content-column .p-name {
    font-size: 2rem;
  }

  .article-content-column .entry-meta-icons-grid {
    gap: 0.5em;
  }

  .my-custom-search {
    margin: 1.5em 0;
  }
}

/* Autocomplete Search Styling */
.my-custom-search {
  margin-top: 2.5em;
  display: flex;
  height: fit-content;
  grid-column: 1;
  grid-row: 1;
  flex-direction: column;
  margin-bottom: 20px;
}

div.asl_m.asl_w {
  height: 2.5em;
  width: 2.5em;
  min-width: 2.5em;
  min-height: 2.5em;
}

/* Category Headers in Content */
article.h-entry .e-content .posts-categories h2 {
  color: theme('colors.ox-black');
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.2em;
  margin: 0;
  text-transform: uppercase;
  display: inline-block;
  border-top: 2px solid theme('colors.ox-green.200');
  padding-top: 0.5em;
}

/* Related Posts */
article.h-entry .ast-related-post-content .post-thumb-img-content img {
  border-radius: 10px;
}

/* Category Posts Container */
.posts-categories {
  display: flex !important;
  flex-direction: column;
}

.posts-category-container {
  display: flex !important;
  flex-direction: column;
  grid-column: 1;
  grid-row: 1;
  margin-top: 6em;
  position: relative;
  z-index: 3;
}

/* Category Accordion Styles */
.posts-category-container > h2 {
  color: theme('colors.ox-black');
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.2em;
  display: flex;
  align-items: center;
  text-transform: uppercase !important;
  border-bottom: 2px solid theme('colors.ox-green.200');
  padding-bottom: 1em;
  padding-left: 0.5em;
  margin-top: 2em;
  margin-bottom: 0;
  letter-spacing: 0.05em;
  position: relative; /* Added for proper positioning of ::before */
}

/* Category Icons */
.posts-category-container h2:nth-of-type(1)::before,
.posts-category-container h2:nth-of-type(2)::before,
.posts-category-container h2:nth-of-type(3)::before,
.posts-category-container h2:nth-of-type(4)::before {
  content: '';
  display: inline-block;
  width: 32px; /* Increased size for better visibility */
  height: 32px; /* Increased size for better visibility */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 10px;
  vertical-align: middle;
  position: relative;
  top: -2px; /* Slight adjustment for vertical alignment */
  background-color: transparent; /* Ensure background is transparent */
  min-width: 32px; /* Ensure minimum width */
  min-height: 32px; /* Ensure minimum height */
}

/* 1. Get Footprint */
.posts-category-container h2:nth-of-type(1)::before {
  background-image: url('/resources/svg/main_get_footprint.svg');
}

/* 2. Offset */
.posts-category-container h2:nth-of-type(2)::before {
  background-image: url('/resources/svg/main_offset.svg');
}

/* 3. Communicate */
.posts-category-container h2:nth-of-type(3)::before {
  background-image: url('/resources/svg/main_communicate.svg');
}

/* 4. General */
.posts-category-container h2:nth-of-type(4)::before {
  background-image: url('/resources/svg/general_comment.svg');
}

/* Accordion Toggle and Label */
.accordion-toggle {
  display: none;
}

.accordion-label {
  cursor: pointer;
  padding: 11px 0 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 5px;
  color: theme('colors.ox-black');
  font-weight: 700;
  text-transform: uppercase !important;
  font-size: 14px;
}

/* Hide the content by default */
.accordion-content {
  display: none;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
}

/* Default closed state icon */
.accordion-label::after {
  content: '';
  display: block;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-25%);
  background-image: url('/resources/svg/general_arrow_head.svg');
  background-size: contain;
  background-repeat: no-repeat;
  width: 10px;
  height: 10px;
  transition: transform 0.3s ease;
}

/* Change icon to open when the accordion is activated */
.accordion-toggle:checked + .accordion-label::after {
  content: '';
  background-image: url('/resources/svg/general_arrow_head.svg');
  transform: translateY(-50%) rotate(180deg);
}

/* Show content when accordion is toggled */
.accordion-toggle:checked + .accordion-label + .accordion-content {
  display: block;
}

/* Style for the currently active post in the list */
.current-post-item a {
  background-color: theme('colors.ox-green.400');
  color: theme('colors.ox-green.600') !important;
  border-radius: 5px;
  padding: 0.5rem 1rem;
  display: block;
  margin: 0.25rem 0;
}

/* Style for regular post items */
.post-item a {
  display: block;
  padding: 0.5rem 1rem;
  margin: 0.25rem 0;
  color: theme('colors.ox-black');
  text-decoration: none;
}

/* Sidebar Search Styles */
.sidebar-search {
  margin: 0 0 1.5rem 0;
  padding: 0;
  width: 100%;
}

/* Styles for Article Sidebar Search - Match header search */
.sidebar-search .is-search-form,
.sidebar-search .is-form-id-19265 {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
}

.sidebar-search .is-search-form .is-search-input,
.is-form-id-19265 .is-search-input,
#is-search-input-19265,
input[id="is-search-input-19265"] {
  color: theme('colors.ox-green.300') !important;
  border-radius: 5px !important;
  padding: 0.8em 1.5em !important;
  font-size: 1rem !important;
  background-color: #fff !important;
  height: 2.5em !important;
  font-weight: 400 !important;
  border: 2px solid theme('colors.ox-green.200') !important;
  display: flex !important;
  width: 100% !important; /* Full width in sidebar */
  flex-grow: 1 !important;
}

.sidebar-search .is-search-form .is-search-input:focus,
.sidebar-search .is-search-form .is-search-input:focus-visible,
.is-form-id-19265 .is-search-input:focus,
.is-form-id-19265 .is-search-input:focus-visible,
#is-search-input-19265:focus,
#is-search-input-19265:focus-visible,
input[id="is-search-input-19265"]:focus,
input[id="is-search-input-19265"]:focus-visible {
  border-color: theme('colors.ox-green.400') !important;
  border: 2px solid theme('colors.ox-green.400') !important;
  color: theme('colors.ox-green.600') !important;
  outline: none !important;
  box-shadow: none !important;
}

.sidebar-search .is-search-form .is-search-submit,
.is-form-id-19265 .is-search-submit,
.sidebar-search input[type="submit"].is-search-submit {
  background-color: theme('colors.ox-green.400') !important;
  color: theme('colors.ox-green.600') !important;
  border: none !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
  padding: 0.5em 1em !important;
  transition: all 0.2s ease-in-out !important;
  border-radius: 5px !important;
  height: 2.5em !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: 0.5em !important;
  white-space: nowrap !important;
  min-width: fit-content !important;
}

.sidebar-search .is-search-form .is-search-submit:hover,
.is-form-id-19265 .is-search-submit:hover,
.sidebar-search input[type="submit"].is-search-submit:hover {
  color: theme('colors.ox-green.600') !important;
  background-color: theme('colors.ox-green.200') !important;
}

.sidebar-search .is-search-form .is-search-submit:focus,
.sidebar-search .is-search-form .is-search-submit:focus-visible,
.is-form-id-19265 .is-search-submit:focus,
.is-form-id-19265 .is-search-submit:focus-visible,
.sidebar-search input[type="submit"].is-search-submit:focus,
.sidebar-search input[type="submit"].is-search-submit:focus-visible {
  outline: 2px solid theme('colors.ox-green.400') !important;
  outline-offset: 2px !important;
  color: theme('colors.ox-green.600') !important;
  background-color: theme('colors.ox-green.200') !important;
}

/* Fix search form layout */
.sidebar-search .is-search-form,
.sidebar-search .is-form-style {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

/* Make sure the search button text is visible and properly sized */
.sidebar-search .is-search-submit span,
.sidebar-search .is-search-submit .is-screen-reader-text {
  position: static !important;
  clip: auto !important;
  width: auto !important;
  height: auto !important;
  font-size: 14px !important;
  line-height: 1 !important;
  clip-path: none !important;
}

/* Ensure consistent font sizing in sidebar search results */
.sidebar-search .is-ajax-search-result .is-ajax-search-post .is-search-content,
.sidebar-search .is-ajax-search-result .is-ajax-search-post .is-search-content *,
.sidebar-search .is-ajax-search-result .is-ajax-search-post .is-search-content .is-highlight,
.sidebar-search .is-ajax-search-result .is-ajax-search-post .is-search-content .is-highlight * {
  font-size: 14px !important;
  line-height: 1.4 !important;
}

/* Highlighted search terms in sidebar search */
#is-ajax-search-result-19265 .is-highlight,
.sidebar-search .is-highlight.term-0 {
  background-color: theme('colors.ox-green.200') !important;
  color: theme('colors.ox-green.600') !important;
  font-size: inherit !important; /* Ensure highlighted text has the same font size */
  font-weight: inherit !important; /* Preserve the original font weight */
  font-family: inherit !important; /* Preserve the original font family */
}

.post-item a:hover {
  background-color: theme('colors.ox-green.200');
}

/* Posts list styling */
.posts-list {
  padding: 0.5rem 0;
  list-style: none;
}

/* Fix Astra messing up empty post pages */
.entry-content[ast-blocks-layout] > * {
  max-width: none;
  margin-left: 0;
  margin-right: 0;
}

/* Hide post navigation */
.ast-separate-container .post-navigation {
  display: none;
}

/* Related Posts Styling */
.ast-related-posts-title-section .ast-related-posts-title {
  padding-top: 2em;
  border-top: 2px solid theme('colors.ox-green.200');
  color: theme('colors.ox-black');
  font-weight: 700;
}

.ast-related-posts-inner-section .entry-header h3 {
  color: theme('colors.ox-black');
  font-weight: 700 !important;
  font-size: 1.75rem !important;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.ast-related-posts-inner-section .entry-content p {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  margin-bottom: 1.5rem;
  overflow-wrap: break-word;
}

.ast-related-post-content .post-thumb-img-content img {
  border-radius: 20px 20px 5px 5px;
}

.ast-related-posts-wrapper .ast-related-post,
.ast-related-post-featured-section {
  background-color: theme('colors.ox-green.100');
  border-radius: 20px;
}

.ast-related-post-content .entry-header .ast-related-post-title a {
  font-weight: 700;
}

/* Related Post Link Styles */
.ast-related-post-cta.read-more .ast-related-post-link {
  text-decoration: none;
  height: 2.5em;
  width: 8em;
  border-radius: 5px;
  background: theme('colors.ox-green.400');
  padding-top: 0.5em;
  color: theme('colors.ox-green.600');
  font-weight: 700;
  align-items: flex-start;
  display: inline-flex;
}

.ast-related-post-cta.read-more .ast-related-post-link:hover {
  background-color: theme('colors.ox-green.200');
  border-color: theme('colors.ox-green.200');
}

.ast-related-post-cta.read-more .ast-related-post-link::before {
  content: 'Read more';
  padding-left: 12px;
}

.ast-related-post-cta.read-more .ast-related-post-link::after {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-left: 5px;
  background-image: url('/resources/svg/general_arrow_head.svg');
  background-size: contain;
  background-repeat: no-repeat;
  content: '';
  transform: rotate(180deg);
}

/* Related Post Meta and Categories */
.ast-related-post-content .entry-meta a {
  color: theme('colors.ox-black');
}

.related-post-categories {
  position: absolute;
  top: 1.5em;
  left: 1.5em;
  display: inline-flex;
  flex-wrap: wrap;
}

/* Primary Category Styles */
.related-post-categories a:first-of-type {
  background-color: theme('colors.ox-green.400');
  color: theme('colors.ox-green.600') !important;
  font-weight: 700;
  text-decoration: none;
  border-radius: 5px;
  margin-right: 5px;
  white-space: nowrap;
  width: min-content;
  padding: 0.3em 0.3em;
  border: 2px solid theme('colors.ox-green.400');
  margin-bottom: 0.1em;
}

.related-post-categories a:first-of-type:hover {
  background-color: theme('colors.ox-green.200');
  border-color: theme('colors.ox-green.200');
}

/* Secondary Category Styles */
.related-post-categories a:nth-child(2) {
  padding: 0.3em 0.3em;
  height: min-content;
  background-color: theme('colors.ox-green.200');
  border: 2px solid theme('colors.ox-green.200');
  color: theme('colors.ox-green.600') !important;
  border-radius: 5px;
  text-decoration: none;
  white-space: nowrap;
  width: min-content;
}

.related-post-categories a:nth-child(2):hover {
  border: 2px solid theme('colors.ox-green.400');
  background-color: theme('colors.ox-green.200');
}

/* Post Content and Search Styles */
.post-content-wrapper {
  text-wrap: wrap;
}

div.asl_w .probox .proinput .orig:focus {
  color: theme('colors.ox-green.400') !important;
}

/* Style first paragraph in main content */
.article-main-content .e-content > p:first-of-type {
  font-weight: 700;
  margin-bottom: 3rem;
  letter-spacing: 0.02em;
}

/* Add spacing between paragraphs */
.article-main-content .e-content p {
  margin-bottom: 2rem;
}

/* Remove margin from last paragraph */
.article-main-content .e-content p:last-child {
  margin-bottom: 0;
}

/* Article Content Layout */
.article-content-layout {
  display: grid;
  grid-template-columns: minmax(300px, 1fr) minmax(600px, 2fr);
  gap: 4rem;
  margin-top: 4rem;
  position: relative;
  z-index: 1;
}

/* Left Sidebar */
.article-sidebar {
  position: relative;
}

/* Hide comments in single posts (articles) */
.single-post .comments-area,
.single-post #comments,
.single-post .comment-respond,
.single-post .comments,
.single-post .comment-form,
.single-post .comment-reply-title,
body.single-post div#comments {
  display: none !important;
}

/* Sidebar search styling */

/* Category Container in Sidebar */
.article-sidebar .posts-category-container {
  margin-top: 0;
  background-color: white;
  padding: 0;
  border-radius: 10px;
}

/* Main Content Area */
.article-main-content {
  background-color: white;
  padding: 1.5rem;
  border-radius: 10px;
  padding-top: 0;
}

/* Responsive Layout */
@media (max-width: 1024px) {
  .article-content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .article-sidebar {
    order: 2;
  }

  .article-main-content {
    order: 1;
    padding-left: 0;
  }
}

/* Tablet-specific styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .project-card .card-footer {
        @apply flex-row items-center justify-end !important;
    }

    .project-card .price-action {
        @apply flex-row items-center justify-end gap-4 w-auto !important;
    }
}

/* Mobile styles */
@media (max-width: 768px) {
    .project-card .card-footer {
        @apply flex-col items-start gap-4 !important;
    }

    .project-card .price-action {
        @apply flex-row items-center justify-between gap-4 w-full;
    }

    .project-card .price {
        @apply text-left;
    }

    .project-card .cart-button {
        @apply justify-center;
    }

    .project-card .ask-for-offer-btn {
        @apply justify-center w-fit !important;
    }
}