# Dependencies
/node_modules
/vendor

# Build files
/public

# Environment files
.env
.env.local
.env.production

# Logs
npm-debug.log
*storybook.log

# IDE files
/.idea
/.vscode

# System files
.DS_Store
Thumbs.db

# Laravel/Acorn specific
/bootstrap/cache/*
!bootstrap/cache/.gitkeep

# Build artifacts (SSR, compiled assets)
/bootstrap/ssr/

# Storage - directories tracked, contents ignored
# (handled by storage/.gitignore)

# Reference materials - accessible to Augment but not in repo
/resources/old-stuff-for-cursor/
